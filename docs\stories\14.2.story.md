# Story 14.2: Midweek Meeting Data Import Interface

## Status
Draft

## Story
**As a** congregation administrator,
**I want** to import midweek meeting data from JW.org through the admin interface,
**so that** I can easily fetch and manage real meeting schedules with a user-friendly interface instead of manually entering data.

## Acceptance Criteria
1. Add "Importar desde JW.org" button to the existing midweek meeting admin interface
2. Create import modal with date range selection (start date, end date) and language dropdown
3. Modal includes import progress indicator and status messages during data fetching
4. Successfully imported meetings appear in the calendar view immediately after import
5. Import functionality integrates with existing purple theme and UI patterns
6. Error handling displays user-friendly messages for import failures
7. Import modal shows summary of imported meetings (count, date range, any skipped)
8. Existing meeting management functionality continues to work unchanged
9. Import button is only visible to users with appropriate admin permissions

## Tasks / Subtasks
- [ ] Add import button to existing admin interface (AC: 1, 9)
  - [ ] Add "Importar desde JW.org" button to calendar header section
  - [ ] Style button with purple theme matching existing design
  - [ ] Add permission check to show button only for admin users
  - [ ] Position button appropriately in existing layout

- [ ] Create import modal component (AC: 2, 5)
  - [ ] Design modal with date range picker components
  - [ ] Add language selection dropdown (Spanish/English)
  - [ ] Implement modal open/close functionality
  - [ ] Style modal consistent with existing purple theme
  - [ ] Add form validation for date ranges

- [ ] Implement import functionality (AC: 3, 4)
  - [ ] Connect modal to POST /api/meetings/midweek/import endpoint
  - [ ] Add loading spinner and progress indicators
  - [ ] Handle API responses and update UI accordingly
  - [ ] Refresh calendar view after successful import
  - [ ] Implement real-time status updates during import

- [ ] Add comprehensive error handling (AC: 6, 7)
  - [ ] Display user-friendly error messages for different failure types
  - [ ] Show import summary with success/failure counts
  - [ ] Handle network errors and timeout scenarios
  - [ ] Provide retry functionality for failed imports

- [ ] Testing and integration (AC: 8)
  - [ ] Test import modal functionality
  - [ ] Verify existing calendar and meeting management works unchanged
  - [ ] Test permission-based button visibility
  - [ ] Test responsive design on mobile devices

## Dev Notes

**Relevant Source Tree Information:**
- `/src/app/admin/meetings/midweek/page.tsx` - Existing admin interface to enhance
- `/src/components/ui/` - Existing UI components for modal, buttons, date pickers
- `/src/hooks/` - Custom hooks for API calls and state management
- `/src/lib/auth.ts` - Authentication utilities for permission checking

**Key Implementation Context:**
- Existing midweek admin interface uses purple theme (#8B5CF6) consistently
- Calendar view already exists and displays meetings from database
- Modal components should follow existing patterns from other admin sections
- Date picker components available in UI library
- Permission checking follows pattern: user.role includes 'elder' or 'ministerial_servant'

**UI Integration Points:**
- Add import button to existing calendar header (next to "Agregar Reunión" button)
- Modal should overlay existing interface without disrupting layout
- Use existing loading spinner components for consistency
- Follow existing error message patterns and styling
- Maintain responsive design for mobile devices

**API Integration:**
- Use existing fetch patterns from other admin components
- Handle loading states with existing UI patterns
- Implement optimistic UI updates for better user experience
- Use existing error handling and toast notification patterns

**Critical Design Requirements:**
- Modal must be accessible (keyboard navigation, screen readers)
- Date range validation (prevent future dates beyond reasonable limit)
- Language selection should default to congregation's preferred language
- Import progress should be clearly visible and informative
- Success/error states should be clearly communicated to user

### Testing

**Test File Location:** `/tests/components/admin/MidweekImportModal.test.tsx`

**Testing Standards:**
- Use React Testing Library for component testing
- Mock API calls to avoid actual JW.org requests
- Test user interactions (button clicks, form submissions)
- Test responsive design across different screen sizes

**Specific Testing Requirements:**
- Test modal open/close functionality
- Test form validation (invalid date ranges, required fields)
- Test permission-based button visibility
- Test loading states and progress indicators
- Test error handling and error message display
- Test successful import flow and calendar refresh
- Mock POST /api/meetings/midweek/import endpoint responses

**Testing Frameworks:**
- React Testing Library for component testing
- Jest for unit tests
- MSW (Mock Service Worker) for API mocking
- Follow existing test patterns from `/tests/components/admin/`

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-01-28 | 1.0 | Initial story creation for import interface enhancement | Product Manager |

## Dev Agent Record
_(This section is owned by dev-agent and can only be modified by dev-agent)_

### Agent Model Used
_(To be populated by dev-agent during implementation)_

### Debug Log References
_(To be populated by dev-agent during implementation)_

### Completion Notes List
_(To be populated by dev-agent during implementation)_

### File List
_(To be populated by dev-agent during implementation)_

## QA Results
_(This section is owned by qa-agent and can only be modified by qa-agent)_

_(To be populated by qa-agent after story completion)_
