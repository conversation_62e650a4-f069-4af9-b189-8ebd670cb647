-- Migration for Advanced Midweek Meeting Management Features
-- Adds tables for meeting templates, assignment conflicts, member availability, and analytics

-- Meeting Templates table for standardizing meeting structures
CREATE TABLE "meeting_templates" (
    "id" TEXT NOT NULL,
    "congregation_id" VARCHAR(8) NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "description" TEXT,
    "part_structure" JSONB NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_by" TEXT NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT NOW(),
    "updated_at" TIMESTAMPTZ(6) NOT NULL DEFAULT NOW(),

    CONSTRAINT "meeting_templates_pkey" PRIMARY KEY ("id")
);

-- Meeting Part Templates for template structure
CREATE TABLE "meeting_part_templates" (
    "id" TEXT NOT NULL,
    "template_id" TEXT NOT NULL,
    "part_type" VARCHAR(100) NOT NULL,
    "title" VARCHAR(255) NOT NULL,
    "duration" INTEGER,
    "requires_assistant" BOOLEAN NOT NULL DEFAULT false,
    "qualification_requirements" TEXT[],
    "display_order" INTEGER NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT NOW(),
    "updated_at" TIMESTAMPTZ(6) NOT NULL DEFAULT NOW(),

    CONSTRAINT "meeting_part_templates_pkey" PRIMARY KEY ("id")
);

-- Assignment Conflicts table for tracking detected conflicts
CREATE TABLE "assignment_conflicts" (
    "id" TEXT NOT NULL,
    "congregation_id" VARCHAR(8) NOT NULL,
    "meeting_id" TEXT NOT NULL,
    "part_id" TEXT,
    "member_id" TEXT NOT NULL,
    "conflict_type" VARCHAR(50) NOT NULL CHECK ("conflict_type" IN ('scheduling', 'qualification', 'availability', 'frequency', 'workload')),
    "severity" VARCHAR(20) NOT NULL CHECK ("severity" IN ('low', 'medium', 'high', 'critical')),
    "conflict_details" TEXT NOT NULL,
    "suggested_resolution" JSONB,
    "status" VARCHAR(20) NOT NULL DEFAULT 'detected' CHECK ("status" IN ('detected', 'resolved', 'ignored')),
    "resolved_by" TEXT,
    "resolved_at" TIMESTAMPTZ(6),
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT NOW(),
    "updated_at" TIMESTAMPTZ(6) NOT NULL DEFAULT NOW(),

    CONSTRAINT "assignment_conflicts_pkey" PRIMARY KEY ("id")
);

-- Member Availability table for tracking member availability preferences
CREATE TABLE "member_availability" (
    "id" TEXT NOT NULL,
    "member_id" TEXT NOT NULL,
    "congregation_id" VARCHAR(8) NOT NULL,
    "availability_type" VARCHAR(20) NOT NULL CHECK ("availability_type" IN ('general', 'blackout', 'preferred')),
    "start_date" DATE NOT NULL,
    "end_date" DATE,
    "day_of_week" INTEGER CHECK ("day_of_week" >= 0 AND "day_of_week" <= 6),
    "part_types" TEXT[],
    "notes" TEXT,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT NOW(),
    "updated_at" TIMESTAMPTZ(6) NOT NULL DEFAULT NOW(),

    CONSTRAINT "member_availability_pkey" PRIMARY KEY ("id")
);

-- Assignment Analytics table for tracking assignment history and performance
CREATE TABLE "assignment_analytics" (
    "id" TEXT NOT NULL,
    "congregation_id" VARCHAR(8) NOT NULL,
    "member_id" TEXT NOT NULL,
    "meeting_id" TEXT NOT NULL,
    "part_id" TEXT NOT NULL,
    "assignment_date" DATE NOT NULL,
    "part_type" VARCHAR(100) NOT NULL,
    "completion_status" VARCHAR(20) NOT NULL DEFAULT 'assigned' CHECK ("completion_status" IN ('assigned', 'completed', 'missed', 'cancelled')),
    "performance_rating" INTEGER CHECK ("performance_rating" >= 1 AND "performance_rating" <= 5),
    "feedback" TEXT,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT NOW(),
    "updated_at" TIMESTAMPTZ(6) NOT NULL DEFAULT NOW(),

    CONSTRAINT "assignment_analytics_pkey" PRIMARY KEY ("id")
);

-- JW.org Cache table for enhanced caching with metadata
CREATE TABLE "jw_org_cache" (
    "id" TEXT NOT NULL,
    "cache_key" VARCHAR(255) NOT NULL UNIQUE,
    "language" VARCHAR(5) NOT NULL,
    "date" DATE NOT NULL,
    "content" JSONB NOT NULL,
    "metadata" JSONB,
    "expires_at" TIMESTAMPTZ(6) NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT NOW(),
    "updated_at" TIMESTAMPTZ(6) NOT NULL DEFAULT NOW(),

    CONSTRAINT "jw_org_cache_pkey" PRIMARY KEY ("id")
);

-- Member Qualifications table for tracking member qualifications and restrictions
CREATE TABLE "member_qualifications" (
    "id" TEXT NOT NULL,
    "member_id" TEXT NOT NULL,
    "congregation_id" VARCHAR(8) NOT NULL,
    "qualifications" TEXT[] NOT NULL DEFAULT '{}',
    "restrictions" TEXT[] NOT NULL DEFAULT '{}',
    "preferred_parts" TEXT[] NOT NULL DEFAULT '{}',
    "notes" TEXT,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT NOW(),
    "updated_at" TIMESTAMPTZ(6) NOT NULL DEFAULT NOW(),

    CONSTRAINT "member_qualifications_pkey" PRIMARY KEY ("id")
);

-- Bulk Assignment Operations table for tracking bulk operations
CREATE TABLE "bulk_assignment_operations" (
    "id" TEXT NOT NULL,
    "congregation_id" VARCHAR(8) NOT NULL,
    "operation_type" VARCHAR(50) NOT NULL,
    "parameters" JSONB NOT NULL,
    "affected_meetings" TEXT[],
    "affected_members" TEXT[],
    "status" VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK ("status" IN ('pending', 'in_progress', 'completed', 'failed')),
    "results" JSONB,
    "error_message" TEXT,
    "created_by" TEXT NOT NULL,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT NOW(),
    "completed_at" TIMESTAMPTZ(6),

    CONSTRAINT "bulk_assignment_operations_pkey" PRIMARY KEY ("id")
);

-- Add indexes for better performance
CREATE INDEX "meeting_templates_congregation_id_idx" ON "meeting_templates"("congregation_id");
CREATE INDEX "meeting_templates_active_idx" ON "meeting_templates"("congregation_id", "is_active");

CREATE INDEX "meeting_part_templates_template_id_idx" ON "meeting_part_templates"("template_id");
CREATE INDEX "meeting_part_templates_order_idx" ON "meeting_part_templates"("template_id", "display_order");

CREATE INDEX "assignment_conflicts_congregation_id_idx" ON "assignment_conflicts"("congregation_id");
CREATE INDEX "assignment_conflicts_meeting_id_idx" ON "assignment_conflicts"("meeting_id");
CREATE INDEX "assignment_conflicts_member_id_idx" ON "assignment_conflicts"("member_id");
CREATE INDEX "assignment_conflicts_status_idx" ON "assignment_conflicts"("congregation_id", "status");
CREATE INDEX "assignment_conflicts_type_severity_idx" ON "assignment_conflicts"("conflict_type", "severity");

CREATE INDEX "member_availability_member_id_idx" ON "member_availability"("member_id");
CREATE INDEX "member_availability_congregation_id_idx" ON "member_availability"("congregation_id");
CREATE INDEX "member_availability_date_range_idx" ON "member_availability"("start_date", "end_date");
CREATE INDEX "member_availability_type_idx" ON "member_availability"("availability_type");

CREATE INDEX "assignment_analytics_congregation_id_idx" ON "assignment_analytics"("congregation_id");
CREATE INDEX "assignment_analytics_member_id_idx" ON "assignment_analytics"("member_id");
CREATE INDEX "assignment_analytics_meeting_id_idx" ON "assignment_analytics"("meeting_id");
CREATE INDEX "assignment_analytics_date_idx" ON "assignment_analytics"("assignment_date");
CREATE INDEX "assignment_analytics_part_type_idx" ON "assignment_analytics"("part_type");

CREATE INDEX "jw_org_cache_key_idx" ON "jw_org_cache"("cache_key");
CREATE INDEX "jw_org_cache_language_date_idx" ON "jw_org_cache"("language", "date");
CREATE INDEX "jw_org_cache_expires_idx" ON "jw_org_cache"("expires_at");

CREATE INDEX "member_qualifications_member_id_idx" ON "member_qualifications"("member_id");
CREATE INDEX "member_qualifications_congregation_id_idx" ON "member_qualifications"("congregation_id");

CREATE INDEX "bulk_assignment_operations_congregation_id_idx" ON "bulk_assignment_operations"("congregation_id");
CREATE INDEX "bulk_assignment_operations_status_idx" ON "bulk_assignment_operations"("status");
CREATE INDEX "bulk_assignment_operations_created_by_idx" ON "bulk_assignment_operations"("created_by");

-- Add foreign key constraints
ALTER TABLE "meeting_part_templates" ADD CONSTRAINT "meeting_part_templates_template_id_fkey" FOREIGN KEY ("template_id") REFERENCES "meeting_templates"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Add constraints for data integrity
ALTER TABLE "meeting_templates" ADD CONSTRAINT "meeting_templates_congregation_id_fkey" FOREIGN KEY ("congregation_id") REFERENCES "congregations"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "assignment_conflicts" ADD CONSTRAINT "assignment_conflicts_congregation_id_fkey" FOREIGN KEY ("congregation_id") REFERENCES "congregations"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "member_availability" ADD CONSTRAINT "member_availability_congregation_id_fkey" FOREIGN KEY ("congregation_id") REFERENCES "congregations"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "assignment_analytics" ADD CONSTRAINT "assignment_analytics_congregation_id_fkey" FOREIGN KEY ("congregation_id") REFERENCES "congregations"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "member_qualifications" ADD CONSTRAINT "member_qualifications_congregation_id_fkey" FOREIGN KEY ("congregation_id") REFERENCES "congregations"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "bulk_assignment_operations" ADD CONSTRAINT "bulk_assignment_operations_congregation_id_fkey" FOREIGN KEY ("congregation_id") REFERENCES "congregations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Add unique constraints
ALTER TABLE "member_qualifications" ADD CONSTRAINT "member_qualifications_member_congregation_unique" UNIQUE ("member_id", "congregation_id");

-- Add check constraints for data validation
ALTER TABLE "member_availability" ADD CONSTRAINT "member_availability_date_range_check" CHECK ("end_date" IS NULL OR "end_date" >= "start_date");
ALTER TABLE "assignment_analytics" ADD CONSTRAINT "assignment_analytics_rating_check" CHECK ("performance_rating" IS NULL OR ("performance_rating" >= 1 AND "performance_rating" <= 5));

-- Create a function to automatically clean up expired cache entries
CREATE OR REPLACE FUNCTION cleanup_expired_cache()
RETURNS void AS $$
BEGIN
    DELETE FROM "jw_org_cache" WHERE "expires_at" < NOW();
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply the trigger to all tables with updated_at columns
CREATE TRIGGER update_meeting_templates_updated_at BEFORE UPDATE ON "meeting_templates" FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_meeting_part_templates_updated_at BEFORE UPDATE ON "meeting_part_templates" FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_assignment_conflicts_updated_at BEFORE UPDATE ON "assignment_conflicts" FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_member_availability_updated_at BEFORE UPDATE ON "member_availability" FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_assignment_analytics_updated_at BEFORE UPDATE ON "assignment_analytics" FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_jw_org_cache_updated_at BEFORE UPDATE ON "jw_org_cache" FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_member_qualifications_updated_at BEFORE UPDATE ON "member_qualifications" FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
