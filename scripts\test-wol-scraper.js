/**
 * Test script for the wol-scraper.js functionality
 * 
 * This script tests the wol-scraper with the new dependencies to ensure
 * it works correctly after removing the simplified version.
 */

const path = require('path');

// Import the wol-scraper
const wolScraperPath = path.join(__dirname, '..', 'wol-scraper.js');

async function testWolScraper() {
  console.log('🧪 Testing WOL Scraper functionality...\n');

  try {
    console.log('📦 Loading wol-scraper.js...');
    const { fetchMidweekMeetingData, fetchWorkbooks } = require(wolScraperPath);

    console.log('✅ WOL Scraper loaded successfully!');

    // Test fetching workbooks
    console.log('\n📚 Testing fetchWorkbooks...');
    try {
      const workbooks = await fetchWorkbooks();
      console.log(`✅ Found ${workbooks.length} workbooks`);
      if (workbooks.length > 0) {
        console.log('📖 Sample workbook:', workbooks[0]);
      }
    } catch (error) {
      console.log('⚠️ fetchWorkbooks failed:', error.message);
    }

    // Test fetching meeting data for current week
    console.log('\n📅 Testing fetchMidweekMeetingData...');
    const currentYear = new Date().getFullYear();
    const currentWeek = Math.ceil((new Date().getTime() - new Date(currentYear, 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000));

    try {
      console.log(`🔍 Fetching data for year ${currentYear}, week ${currentWeek}...`);
      const meetingData = await fetchMidweekMeetingData(currentYear, currentWeek);
      
      if (meetingData) {
        console.log('✅ Meeting data fetched successfully!');
        console.log('📊 Meeting data structure:');
        console.log('  - Date:', meetingData.date);
        console.log('  - Scripture:', meetingData.scripture);
        console.log('  - Sections:', Object.keys(meetingData.sections || {}));
        
        // Show sample parts
        if (meetingData.sections) {
          Object.entries(meetingData.sections).forEach(([sectionName, parts]) => {
            console.log(`  - ${sectionName}: ${parts.length} parts`);
            if (parts.length > 0) {
              console.log(`    Sample: ${parts[0].title}`);
            }
          });
        }
      } else {
        console.log('⚠️ No meeting data available for this week');
      }
    } catch (error) {
      console.log('❌ fetchMidweekMeetingData failed:', error.message);
      console.log('Stack trace:', error.stack);
    }

    console.log('\n🎉 WOL Scraper test completed!');

  } catch (error) {
    console.error('❌ Failed to load wol-scraper.js:', error.message);
    console.error('Stack trace:', error.stack);
    
    // Check if dependencies exist
    console.log('\n🔍 Checking dependencies...');
    
    const songTitlesPath = path.join(__dirname, '..', 'song-titles.js');
    const songServicePath = path.join(__dirname, '..', 'services', 'song-title-service.js');
    
    try {
      require(songTitlesPath);
      console.log('✅ song-titles.js found and loadable');
    } catch (err) {
      console.log('❌ song-titles.js issue:', err.message);
    }
    
    try {
      require(songServicePath);
      console.log('✅ song-title-service.js found and loadable');
    } catch (err) {
      console.log('❌ song-title-service.js issue:', err.message);
    }
  }
}

// Run the test
if (require.main === module) {
  testWolScraper().catch(console.error);
}

module.exports = { testWolScraper };
