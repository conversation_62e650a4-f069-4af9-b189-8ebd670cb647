/**
 * Dynamic Song Title Service
 * 
 * Provides dynamic song title fetching from JW.org with fallback mechanisms.
 * This service attempts to fetch song titles directly from JW.org pages.
 */

const puppeteer = require('puppeteer');

/**
 * Get dynamic song title from JW.org
 * @param {number} songNumber - The song number
 * @param {string} language - Language code ('es', 'en')
 * @param {string} fullSongUrl - Full URL to the song page (optional)
 * @returns {Promise<string>} Song title
 */
async function getDynamicSongTitle(songNumber, language = 'es', fullSongUrl = null) {
  let browser = null;
  
  try {
    console.log(`Fetching dynamic song title for song ${songNumber} in ${language}...`);

    // If no URL provided, construct it
    if (!fullSongUrl) {
      const baseUrl = language === 'es' 
        ? 'https://www.jw.org/es/biblioteca/canciones'
        : 'https://www.jw.org/en/library/songs';
      fullSongUrl = `${baseUrl}/song-${songNumber.toString().padStart(3, '0')}/`;
    }

    console.log(`Attempting to fetch from: ${fullSongUrl}`);

    browser = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-http2'],
      timeout: 30000,
    });

    const page = await browser.newPage();
    
    // Set user agent to avoid blocking
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
    
    // Navigate to the song page
    const response = await page.goto(fullSongUrl, {
      waitUntil: 'networkidle2',
      timeout: 30000,
    });

    if (!response || response.status() !== 200) {
      throw new Error(`Failed to load page: ${response ? response.status() : 'No response'}`);
    }

    // Try different selectors for song title
    const titleSelectors = [
      'h1.publicationTitle',
      'h1.jsTitle',
      'h1[data-title]',
      '.publicationTitle',
      '.jsTitle',
      'h1',
      '.title',
    ];

    let songTitle = null;

    for (const selector of titleSelectors) {
      try {
        const element = await page.$(selector);
        if (element) {
          songTitle = await page.evaluate(el => el.textContent?.trim(), element);
          if (songTitle && songTitle.length > 0) {
            console.log(`Found title with selector "${selector}": ${songTitle}`);
            break;
          }
        }
      } catch (selectorError) {
        console.log(`Selector "${selector}" failed:`, selectorError.message);
      }
    }

    if (!songTitle) {
      throw new Error('Could not find song title on page');
    }

    // Clean up the title
    songTitle = songTitle.replace(/^\d+\.\s*/, ''); // Remove leading number
    songTitle = songTitle.trim();

    console.log(`Successfully fetched song title: "${songTitle}"`);
    return songTitle;

  } catch (error) {
    console.error(`Error fetching dynamic song title for ${songNumber}:`, error.message);
    
    // Return fallback title
    const fallbackTitle = language === 'es' ? `Canción ${songNumber}` : `Song ${songNumber}`;
    console.log(`Using fallback title: "${fallbackTitle}"`);
    return fallbackTitle;

  } finally {
    if (browser) {
      try {
        await browser.close();
      } catch (closeError) {
        console.error('Error closing browser:', closeError);
      }
    }
  }
}

/**
 * Batch fetch song titles
 * @param {number[]} songNumbers - Array of song numbers
 * @param {string} language - Language code
 * @returns {Promise<Object>} Object with song numbers as keys and titles as values
 */
async function batchFetchSongTitles(songNumbers, language = 'es') {
  const results = {};
  
  for (const songNumber of songNumbers) {
    try {
      const title = await getDynamicSongTitle(songNumber, language);
      results[songNumber] = title;
      
      // Add delay between requests to be respectful
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error(`Failed to fetch title for song ${songNumber}:`, error);
      results[songNumber] = language === 'es' ? `Canción ${songNumber}` : `Song ${songNumber}`;
    }
  }
  
  return results;
}

/**
 * Validate song URL
 * @param {string} url - URL to validate
 * @returns {boolean} True if URL appears to be a valid JW.org song URL
 */
function isValidSongUrl(url) {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname === 'www.jw.org' && 
           (url.includes('/songs/') || url.includes('/canciones/'));
  } catch {
    return false;
  }
}

module.exports = {
  getDynamicSongTitle,
  batchFetchSongTitles,
  isValidSongUrl,
};
