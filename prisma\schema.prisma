generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum TerritoryStatus {
  available
  assigned
  completed
  out_of_service
  unavailable
}

enum AssignmentStatus {
  active
  completed
  overdue
  cancelled
}

model HealthCheck {
  id        String   @id @default(cuid())
  status    String   @default("ok")
  timestamp DateTime @default(now())

  @@map("health_checks")
}

model Congregation {
  id                       String                    @id @db.VarChar(8)
  name                     String                    @db.VarChar(255)
  region                   String?                   @db.VarChar(50)
  pin                      String                    @db.VarChar(255)
  language                 String                    @default("es") @db.VarChar(5)
  timezone                 String                    @default("America/Mexico_City") @db.VarChar(50)
  settings                 Json                      @default("{}")
  isActive                 Boolean                   @default(true) @map("is_active")
  createdAt                DateTime                  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt                DateTime                  @updatedAt @map("updated_at") @db.Timestamptz(6)
  assignmentHistory        AssignmentHistory[]
  communicationPreferences CommunicationPreference[]
  communicationTemplates   CommunicationTemplate[]
  congregationSettings     CongregationSetting[]
  documentFolders          DocumentFolder[]
  elderPermissions         ElderPermission[]
  permissionAuditLogs      PermissionAuditLog[]
  events                   Event[]
  fieldServiceRecords      FieldServiceRecord[]
  letters                  Letter[]
  memberChangeHistory      MemberChangeHistory[]
  members                  Member[]
  midweekMeetings          MidweekMeeting[]
  notifications            Notification[]
  pinChangeHistory         PinChangeHistory[]
  pinSettings              PinSettings?
  sectionAssignments       SectionAssignment[]
  serviceGroups            ServiceGroup[]
  serviceSchedules         ServiceSchedule[]
  taskAssignments          TaskAssignment[]
  tasks                    Task[]
  territories              Territory[]
  territoryAssignments     TerritoryAssignment[]
  territoryVisits          TerritoryVisit[]
  weekendMeetings          WeekendMeeting[]
  temporaryPins            TemporaryPin[]
  accountLockouts          AccountLockout[]
  securityAuditEvents      SecurityAuditEvent[]
  meetingTemplates         MeetingTemplate[]
  assignmentConflicts      AssignmentConflict[]
  memberAvailability       MemberAvailability[]
  assignmentAnalytics      AssignmentAnalytics[]
  memberQualifications     MemberQualifications[]
  bulkAssignmentOperations BulkAssignmentOperation[]

  @@map("congregations")
}

model Role {
  id          String   @id @default(cuid())
  name        String   @unique @db.VarChar(50)
  description String?
  permissions Json     @default("[]")
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt   DateTime @updatedAt @map("updated_at") @db.Timestamptz(6)
  members     Member[]

  @@map("roles")
}

model Member {
  id                          String                    @id @default(cuid())
  congregationId              String                    @map("congregation_id") @db.VarChar(8)
  name                        String                    @db.VarChar(255)
  email                       String?                   @db.VarChar(255)
  phone                       String?                   @db.VarChar(20)
  address                     String?                   @db.Text
  birthDate                   DateTime?                 @map("birth_date") @db.Date
  role                        String                    @default("publisher") @db.VarChar(50)
  serviceGroup                String?                   @map("service_group") @db.VarChar(100)
  pin                         String                    @db.VarChar(255)
  isActive                    Boolean                   @default(true) @map("is_active")
  lastLogin                   DateTime?                 @map("last_login") @db.Timestamptz(6)
  preferences                 Json                      @default("{}")
  contactPreferences          Json?                     @map("contact_preferences") @default("{}")
  qualifications              Json?                     @default("[]")
  notes                       String?                   @db.Text
  createdAt                   DateTime                  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt                   DateTime                  @updatedAt @map("updated_at") @db.Timestamptz(6)
  assignmentHistoryAssignedBy AssignmentHistory[]       @relation("AssignmentHistoryAssignedBy")
  assignmentHistoryAssignedTo AssignmentHistory[]       @relation("AssignmentHistoryAssignedTo")
  assignmentHistory           AssignmentHistory[]
  communicationPreferences    CommunicationPreference[]
  documentAccessLogs          DocumentAccessLog[]
  documentComments            DocumentComment[]
  createdWorkflows            DocumentWorkflow[]        @relation("WorkflowAssignedBy")
  assignedWorkflows           DocumentWorkflow[]        @relation("WorkflowAssignedTo")
  elderPermissions            ElderPermission[]
  assignedElderPermissions    ElderPermission[]             @relation("ElderPermissionAssignedBy")
  permissionAuditUser         PermissionAuditLog[]          @relation("PermissionAuditUser")
  permissionAuditPerformedBy  PermissionAuditLog[]          @relation("PermissionAuditPerformedBy")
  fieldServiceRecords         FieldServiceRecord[]
  approvedLetters             Letter[]                  @relation("ApprovedBy")
  uploadedLetters             Letter[]                  @relation("UploadedBy")
  changedByHistory            MemberChangeHistory[]     @relation("ChangedBy")
  memberChangeHistory         MemberChangeHistory[]
  congregation                Congregation              @relation(fields: [congregationId], references: [id], onDelete: Cascade)
  roleRef                     Role                      @relation(fields: [role], references: [name])
  receivedNotifications       Notification[]            @relation("NotificationRecipient")
  sentNotifications           Notification[]            @relation("NotificationSender")
  pinChangedByHistory         PinChangeHistory[]        @relation("PinChangedBy")
  pinChangeHistory            PinChangeHistory[]
  createdPinSettings          PinSettings[]             @relation("CreatedBy")
  assignedSections            SectionAssignment[]       @relation("AssignedBy")
  sectionAssignments          SectionAssignment[]
  taskAssignments             TaskAssignment[]
  territoryAssignments        TerritoryAssignment[]     @relation("TerritoryAssignmentMember")
  assignedTerritoryAssignments TerritoryAssignment[]    @relation("TerritoryAssignmentAssignedBy")
  overseenServiceGroups       ServiceGroup[]            @relation("ServiceGroupOverseer")
  assistedServiceGroups       ServiceGroup[]            @relation("ServiceGroupAssistant")
  groupAssignments            GroupAssignment[]
  conductedServiceTimes       ServiceScheduleTime[]
  temporaryPins               TemporaryPin[]            @relation("TemporaryPinMember")
  createdTemporaryPins        TemporaryPin[]            @relation("TemporaryPinCreatedBy")
  accountLockouts             AccountLockout[]          @relation("AccountLockoutMember")
  unlockedAccounts            AccountLockout[]          @relation("AccountLockoutUnlockedBy")
  securityAuditEvents         SecurityAuditEvent[]     @relation("SecurityAuditMember")
  performedSecurityAudits     SecurityAuditEvent[]     @relation("SecurityAuditPerformedBy")

  @@unique([congregationId, email])
  @@index([congregationId, role])
  @@index([congregationId, isActive])
  @@index([congregationId, serviceGroup])
  @@map("members")
}

model MemberChangeHistory {
  id              String       @id @default(cuid())
  congregationId  String       @map("congregation_id") @db.VarChar(8)
  memberId        String       @map("member_id")
  changedBy       String       @map("changed_by")
  changeType      String       @map("change_type") @db.VarChar(50)
  fieldName       String?      @map("field_name") @db.VarChar(100)
  oldValue        String?      @map("old_value")
  newValue        String?      @map("new_value")
  reason          String?
  createdAt       DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  changedByMember Member       @relation("ChangedBy", fields: [changedBy], references: [id])
  congregation    Congregation @relation(fields: [congregationId], references: [id], onDelete: Cascade)
  member          Member       @relation(fields: [memberId], references: [id], onDelete: Cascade)

  @@index([congregationId, memberId])
  @@index([congregationId, changeType])
  @@index([createdAt])
  @@map("member_change_history")
}

model PinSettings {
  id                           String       @id @default(cuid())
  congregationId               String       @unique @map("congregation_id") @db.VarChar(8)
  minLength                    Int          @default(4) @map("min_length")
  maxLength                    Int          @default(8) @map("max_length")
  requireNumeric               Boolean      @default(true) @map("require_numeric")
  requireAlphanumeric          Boolean      @default(false) @map("require_alphanumeric")
  requireSpecialChars          Boolean      @default(false) @map("require_special_chars")
  allowSequential              Boolean      @default(true) @map("allow_sequential")
  allowRepeated                Boolean      @default(true) @map("allow_repeated")
  expirationDays               Int?         @map("expiration_days")
  bcryptRounds                 Int          @default(12) @map("bcrypt_rounds")
  maxAttempts                  Int          @default(5) @map("max_attempts")
  lockoutDurationMinutes       Int          @default(30) @map("lockout_duration_minutes")
  preventReuseCount            Int          @default(3) @map("prevent_reuse_count")
  temporaryPinExpirationHours  Int          @default(24) @map("temporary_pin_expiration_hours")
  createdBy                    String?      @map("created_by")
  createdAt                    DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt                    DateTime     @updatedAt @map("updated_at") @db.Timestamptz(6)
  congregation                 Congregation @relation(fields: [congregationId], references: [id], onDelete: Cascade)
  createdByMember              Member?      @relation("CreatedBy", fields: [createdBy], references: [id])

  @@map("pin_settings")
}

model PinChangeHistory {
  id              String       @id @default(cuid())
  congregationId  String       @map("congregation_id") @db.VarChar(8)
  memberId        String?      @map("member_id")
  changedBy       String       @map("changed_by")
  changeType      String       @map("change_type") @db.VarChar(50)
  oldPinHash      String?      @map("old_pin_hash") @db.VarChar(255)
  newPinHash      String?      @map("new_pin_hash") @db.VarChar(255)
  reason          String?
  ipAddress       String?      @map("ip_address") @db.VarChar(45)
  userAgent       String?      @map("user_agent")
  createdAt       DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  changedByMember Member       @relation("PinChangedBy", fields: [changedBy], references: [id])
  congregation    Congregation @relation(fields: [congregationId], references: [id], onDelete: Cascade)
  member          Member?      @relation(fields: [memberId], references: [id], onDelete: Cascade)

  @@index([congregationId, memberId])
  @@index([congregationId, changeType])
  @@index([createdAt])
  @@map("pin_change_history")
}

model TemporaryPin {
  id                String       @id @default(cuid())
  congregationId    String       @map("congregation_id") @db.VarChar(8)
  memberId          String       @map("member_id")
  temporaryPinHash  String       @map("temporary_pin_hash") @db.VarChar(255)
  resetType         String       @default("temporary") @map("reset_type") @db.VarChar(50)
  createdBy         String       @map("created_by")
  reason            String?
  expirationDate    DateTime     @map("expiration_date") @db.Timestamptz(6)
  requireChange     Boolean      @default(true) @map("require_change")
  used              Boolean      @default(false)
  usedAt            DateTime?    @map("used_at") @db.Timestamptz(6)
  ipAddress         String?      @map("ip_address") @db.VarChar(45)
  userAgent         String?      @map("user_agent")
  createdAt         DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt         DateTime     @updatedAt @map("updated_at") @db.Timestamptz(6)
  congregation      Congregation @relation(fields: [congregationId], references: [id], onDelete: Cascade)
  member            Member       @relation("TemporaryPinMember", fields: [memberId], references: [id], onDelete: Cascade)
  createdByMember   Member       @relation("TemporaryPinCreatedBy", fields: [createdBy], references: [id], onDelete: Cascade)

  @@index([congregationId, memberId])
  @@index([expirationDate])
  @@index([used, expirationDate])
  @@unique([congregationId, memberId], name: "active_temporary_pin")
  @@map("temporary_pins")
}

model AccountLockout {
  id                String       @id @default(cuid())
  congregationId    String       @map("congregation_id") @db.VarChar(8)
  memberId          String       @map("member_id")
  lockoutReason     String       @map("lockout_reason") @db.VarChar(100)
  attemptCount      Int          @default(0) @map("attempt_count")
  lockedAt          DateTime     @default(now()) @map("locked_at") @db.Timestamptz(6)
  unlockAt          DateTime     @map("unlock_at") @db.Timestamptz(6)
  unlockedBy        String?      @map("unlocked_by")
  unlockedAt        DateTime?    @map("unlocked_at") @db.Timestamptz(6)
  isActive          Boolean      @default(true) @map("is_active")
  ipAddress         String?      @map("ip_address") @db.VarChar(45)
  userAgent         String?      @map("user_agent")
  createdAt         DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt         DateTime     @updatedAt @map("updated_at") @db.Timestamptz(6)
  congregation      Congregation @relation(fields: [congregationId], references: [id], onDelete: Cascade)
  member            Member       @relation("AccountLockoutMember", fields: [memberId], references: [id], onDelete: Cascade)
  unlockedByMember  Member?      @relation("AccountLockoutUnlockedBy", fields: [unlockedBy], references: [id], onDelete: SetNull)

  @@index([congregationId, memberId])
  @@index([isActive, unlockAt])
  @@map("account_lockouts")
}

model SecurityAuditEvent {
  id             String       @id @default(cuid())
  congregationId String       @map("congregation_id") @db.VarChar(8)
  memberId       String?      @map("member_id")
  eventType      String       @map("event_type") @db.VarChar(100)
  success        Boolean
  ipAddress      String?      @map("ip_address") @db.VarChar(45)
  userAgent      String?      @map("user_agent")
  details        Json?
  performedBy    String?      @map("performed_by")
  timestamp      DateTime     @default(now()) @db.Timestamptz(6)
  createdAt      DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  congregation   Congregation @relation(fields: [congregationId], references: [id], onDelete: Cascade)
  member         Member?      @relation("SecurityAuditMember", fields: [memberId], references: [id], onDelete: SetNull)
  performedByMember Member?   @relation("SecurityAuditPerformedBy", fields: [performedBy], references: [id], onDelete: SetNull)

  @@index([congregationId])
  @@index([memberId])
  @@index([eventType])
  @@index([timestamp])
  @@map("security_audit_events")
}

model ElderPermission {
  id             String       @id @default(cuid())
  congregationId String       @map("congregation_id") @db.VarChar(8)
  memberId       String       @map("member_id")
  sectionId      String       @map("section_id") @db.VarChar(100)
  permissions    Json         @default("[]") // Array of specific permissions
  assignedBy     String       @map("assigned_by")
  assignedAt     DateTime     @default(now()) @map("assigned_at") @db.Timestamptz(6)
  expirationDate DateTime?    @map("expiration_date") @db.Timestamptz(6)
  isActive       Boolean      @default(true) @map("is_active")
  notes          String?      @db.Text
  createdAt      DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt      DateTime     @updatedAt @map("updated_at") @db.Timestamptz(6)
  congregation   Congregation @relation(fields: [congregationId], references: [id], onDelete: Cascade)
  member         Member       @relation(fields: [memberId], references: [id], onDelete: Cascade)
  assignedByMember Member     @relation("ElderPermissionAssignedBy", fields: [assignedBy], references: [id])

  @@unique([memberId, sectionId])
  @@index([congregationId, sectionId])
  @@index([assignedBy])
  @@index([isActive])
  @@map("elder_permissions")
}

model PermissionAuditLog {
  id             String       @id @default(cuid())
  congregationId String       @map("congregation_id") @db.VarChar(8)
  userId         String       @map("user_id")
  action         String       @db.VarChar(50) // 'assign', 'revoke', 'modify', 'expire'
  sectionId      String       @map("section_id") @db.VarChar(100)
  permissions    Json         @default("[]") // Array of permissions affected
  performedBy    String       @map("performed_by")
  reason         String?      @db.Text
  ipAddress      String?      @map("ip_address") @db.VarChar(45)
  userAgent      String?      @map("user_agent") @db.Text
  timestamp      DateTime     @default(now()) @db.Timestamptz(6)
  createdAt      DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  congregation   Congregation @relation(fields: [congregationId], references: [id], onDelete: Cascade)
  user           Member       @relation("PermissionAuditUser", fields: [userId], references: [id], onDelete: Cascade)
  performedByMember Member    @relation("PermissionAuditPerformedBy", fields: [performedBy], references: [id], onDelete: Cascade)

  @@index([congregationId, userId])
  @@index([congregationId, sectionId])
  @@index([performedBy])
  @@index([timestamp])
  @@map("permission_audit_logs")
}

model SectionAssignment {
  id               String       @id @default(cuid())
  congregationId   String       @map("congregation_id") @db.VarChar(8)
  memberId         String       @map("member_id")
  sectionType      String       @map("section_type") @db.VarChar(100)
  scopeDefinition  Json         @default("{}") @map("scope_definition")
  assignedBy       String?      @map("assigned_by")
  assignedAt       DateTime     @default(now()) @map("assigned_at") @db.Timestamptz(6)
  isActive         Boolean      @default(true) @map("is_active")
  createdAt        DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt        DateTime     @updatedAt @map("updated_at") @db.Timestamptz(6)
  assignedByMember Member?      @relation("AssignedBy", fields: [assignedBy], references: [id])
  congregation     Congregation @relation(fields: [congregationId], references: [id], onDelete: Cascade)
  member           Member       @relation(fields: [memberId], references: [id], onDelete: Cascade)

  @@unique([memberId, sectionType])
  @@index([congregationId, sectionType])
  @@index([congregationId, isActive])
  @@map("section_assignments")
}

model AssignmentHistory {
  id               String       @id @default(cuid())
  congregationId   String       @map("congregation_id") @db.VarChar(8)
  memberId         String       @map("member_id")
  sectionType      String       @map("section_type") @db.VarChar(100)
  action           String       @db.VarChar(50)
  assignedBy       String?      @map("assigned_by")
  assignedTo       String?      @map("assigned_to")
  reason           String?
  createdAt        DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  assignedByMember Member?      @relation("AssignmentHistoryAssignedBy", fields: [assignedBy], references: [id])
  assignedToMember Member?      @relation("AssignmentHistoryAssignedTo", fields: [assignedTo], references: [id])
  congregation     Congregation @relation(fields: [congregationId], references: [id], onDelete: Cascade)
  member           Member       @relation(fields: [memberId], references: [id], onDelete: Cascade)

  @@index([congregationId, memberId])
  @@index([congregationId, sectionType])
  @@index([createdAt])
  @@map("assignment_history")
}

model MidweekMeeting {
  id             String               @id @default(cuid())
  congregationId String               @map("congregation_id") @db.VarChar(8)
  meetingDate    DateTime             @map("meeting_date") @db.Date
  chairman       String?              @db.VarChar(255)
  openingPrayer  String?              @map("opening_prayer") @db.VarChar(255)
  closingPrayer  String?              @map("closing_prayer") @db.VarChar(255)
  location       String               @default("Kingdom Hall") @db.VarChar(100)
  zoomLink       String?              @map("zoom_link")
  notes          String?
  isActive       Boolean              @default(true) @map("is_active")
  createdAt      DateTime             @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt      DateTime             @updatedAt @map("updated_at") @db.Timestamptz(6)
  parts          MidweekMeetingPart[]
  congregation   Congregation         @relation(fields: [congregationId], references: [id], onDelete: Cascade)

  @@index([congregationId, meetingDate])
  @@map("midweek_meetings")
}

model MidweekMeetingPart {
  id             String         @id @default(cuid())
  meetingId      String         @map("meeting_id")
  partNumber     Int            @map("part_number")
  partType       String         @map("part_type") @db.VarChar(100)
  title          String         @db.VarChar(255)
  assignedMember String?        @map("assigned_member") @db.VarChar(255)
  assistant      String?        @db.VarChar(255)
  timeAllocation Int?           @map("time_allocation")
  notes          String?
  isCompleted    Boolean        @default(false) @map("is_completed")
  createdAt      DateTime       @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt      DateTime       @updatedAt @map("updated_at") @db.Timestamptz(6)
  meeting        MidweekMeeting @relation(fields: [meetingId], references: [id], onDelete: Cascade)

  @@index([meetingId, partNumber])
  @@map("midweek_meeting_parts")
}

model WeekendMeeting {
  id                  String               @id @default(cuid())
  congregationId      String               @map("congregation_id") @db.VarChar(8)
  meetingDate         DateTime             @map("meeting_date") @db.Date
  publicTalkTitle     String?              @map("public_talk_title") @db.VarChar(255)
  publicTalkSpeaker   String?              @map("public_talk_speaker") @db.VarChar(255)
  speakerCongregation String?              @map("speaker_congregation") @db.VarChar(255)
  watchtowerConductor String?              @map("watchtower_conductor") @db.VarChar(255)
  watchtowerReader    String?              @map("watchtower_reader") @db.VarChar(255)
  chairman            String?              @db.VarChar(255)
  openingPrayer       String?              @map("opening_prayer") @db.VarChar(255)
  closingPrayer       String?              @map("closing_prayer") @db.VarChar(255)
  location            String               @default("Kingdom Hall") @db.VarChar(100)
  zoomLink            String?              @map("zoom_link")
  notes               String?
  isActive            Boolean              @default(true) @map("is_active")
  createdAt           DateTime             @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt           DateTime             @updatedAt @map("updated_at") @db.Timestamptz(6)
  parts               WeekendMeetingPart[]
  congregation        Congregation         @relation(fields: [congregationId], references: [id], onDelete: Cascade)

  @@index([congregationId, meetingDate])
  @@map("weekend_meetings")
}

model WeekendMeetingPart {
  id             String         @id @default(cuid())
  meetingId      String         @map("meeting_id")
  partType       String         @map("part_type") @db.VarChar(100)
  assignedMember String?        @map("assigned_member") @db.VarChar(255)
  notes          String?
  isCompleted    Boolean        @default(false) @map("is_completed")
  createdAt      DateTime       @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt      DateTime       @updatedAt @map("updated_at") @db.Timestamptz(6)
  meeting        WeekendMeeting @relation(fields: [meetingId], references: [id], onDelete: Cascade)

  @@index([meetingId, partType])
  @@map("weekend_meeting_parts")
}

// Advanced Meeting Management Models

model MeetingTemplate {
  id             String                 @id @default(cuid())
  congregationId String                 @map("congregation_id") @db.VarChar(8)
  name           String                 @db.VarChar(255)
  description    String?
  partStructure  Json                   @map("part_structure")
  isActive       Boolean                @default(true) @map("is_active")
  createdBy      String                 @map("created_by")
  createdAt      DateTime               @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt      DateTime               @updatedAt @map("updated_at") @db.Timestamptz(6)
  congregation   Congregation           @relation(fields: [congregationId], references: [id], onDelete: Cascade)
  parts          MeetingPartTemplate[]

  @@index([congregationId])
  @@index([congregationId, isActive])
  @@map("meeting_templates")
}

model MeetingPartTemplate {
  id                        String          @id @default(cuid())
  templateId                String          @map("template_id")
  partType                  String          @map("part_type") @db.VarChar(100)
  title                     String          @db.VarChar(255)
  duration                  Int?
  requiresAssistant         Boolean         @default(false) @map("requires_assistant")
  qualificationRequirements String[]        @map("qualification_requirements")
  displayOrder              Int             @map("display_order")
  createdAt                 DateTime        @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt                 DateTime        @updatedAt @map("updated_at") @db.Timestamptz(6)
  template                  MeetingTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)

  @@index([templateId])
  @@index([templateId, displayOrder])
  @@map("meeting_part_templates")
}

model AssignmentConflict {
  id               String       @id @default(cuid())
  congregationId   String       @map("congregation_id") @db.VarChar(8)
  meetingId        String       @map("meeting_id")
  partId           String?      @map("part_id")
  memberId         String       @map("member_id")
  conflictType     String       @map("conflict_type") @db.VarChar(50)
  severity         String       @db.VarChar(20)
  conflictDetails  String       @map("conflict_details")
  suggestedResolution Json?     @map("suggested_resolution")
  status           String       @default("detected") @db.VarChar(20)
  resolvedBy       String?      @map("resolved_by")
  resolvedAt       DateTime?    @map("resolved_at") @db.Timestamptz(6)
  createdAt        DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt        DateTime     @updatedAt @map("updated_at") @db.Timestamptz(6)
  congregation     Congregation @relation(fields: [congregationId], references: [id], onDelete: Cascade)

  @@index([congregationId])
  @@index([meetingId])
  @@index([memberId])
  @@index([congregationId, status])
  @@index([conflictType, severity])
  @@map("assignment_conflicts")
}

model MemberAvailability {
  id               String       @id @default(cuid())
  memberId         String       @map("member_id")
  congregationId   String       @map("congregation_id") @db.VarChar(8)
  availabilityType String       @map("availability_type") @db.VarChar(20)
  startDate        DateTime     @map("start_date") @db.Date
  endDate          DateTime?    @map("end_date") @db.Date
  dayOfWeek        Int?         @map("day_of_week")
  partTypes        String[]     @map("part_types")
  notes            String?
  createdAt        DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt        DateTime     @updatedAt @map("updated_at") @db.Timestamptz(6)
  congregation     Congregation @relation(fields: [congregationId], references: [id], onDelete: Cascade)

  @@index([memberId])
  @@index([congregationId])
  @@index([startDate, endDate])
  @@index([availabilityType])
  @@map("member_availability")
}

model AssignmentAnalytics {
  id               String       @id @default(cuid())
  congregationId   String       @map("congregation_id") @db.VarChar(8)
  memberId         String       @map("member_id")
  meetingId        String       @map("meeting_id")
  partId           String       @map("part_id")
  assignmentDate   DateTime     @map("assignment_date") @db.Date
  partType         String       @map("part_type") @db.VarChar(100)
  completionStatus String       @default("assigned") @map("completion_status") @db.VarChar(20)
  performanceRating Int?        @map("performance_rating")
  feedback         String?
  createdAt        DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt        DateTime     @updatedAt @map("updated_at") @db.Timestamptz(6)
  congregation     Congregation @relation(fields: [congregationId], references: [id], onDelete: Cascade)

  @@index([congregationId])
  @@index([memberId])
  @@index([meetingId])
  @@index([assignmentDate])
  @@index([partType])
  @@map("assignment_analytics")
}

model JwOrgCache {
  id        String   @id @default(cuid())
  cacheKey  String   @unique @map("cache_key") @db.VarChar(255)
  language  String   @db.VarChar(5)
  date      DateTime @db.Date
  content   Json
  metadata  Json?
  expiresAt DateTime @map("expires_at") @db.Timestamptz(6)
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt DateTime @updatedAt @map("updated_at") @db.Timestamptz(6)

  @@index([cacheKey])
  @@index([language, date])
  @@index([expiresAt])
  @@map("jw_org_cache")
}

model MemberQualifications {
  id             String       @id @default(cuid())
  memberId       String       @map("member_id")
  congregationId String       @map("congregation_id") @db.VarChar(8)
  qualifications String[]     @default([])
  restrictions   String[]     @default([])
  preferredParts String[]     @default([]) @map("preferred_parts")
  notes          String?
  createdAt      DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt      DateTime     @updatedAt @map("updated_at") @db.Timestamptz(6)
  congregation   Congregation @relation(fields: [congregationId], references: [id], onDelete: Cascade)

  @@unique([memberId, congregationId])
  @@index([memberId])
  @@index([congregationId])
  @@map("member_qualifications")
}

model BulkAssignmentOperation {
  id               String       @id @default(cuid())
  congregationId   String       @map("congregation_id") @db.VarChar(8)
  operationType    String       @map("operation_type") @db.VarChar(50)
  parameters       Json
  affectedMeetings String[]     @map("affected_meetings")
  affectedMembers  String[]     @map("affected_members")
  status           String       @default("pending") @db.VarChar(20)
  results          Json?
  errorMessage     String?      @map("error_message")
  createdBy        String       @map("created_by")
  createdAt        DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  completedAt      DateTime?    @map("completed_at") @db.Timestamptz(6)
  congregation     Congregation @relation(fields: [congregationId], references: [id], onDelete: Cascade)

  @@index([congregationId])
  @@index([status])
  @@index([createdBy])
  @@map("bulk_assignment_operations")
}

model Task {
  id             String           @id @default(cuid())
  congregationId String           @map("congregation_id") @db.VarChar(8)
  title          String           @db.VarChar(255)
  description    String?
  category       String           @db.VarChar(100)
  frequency      String           @db.VarChar(50)
  estimatedTime  Int?             @map("estimated_time")
  instructions   String?
  isActive       Boolean          @default(true) @map("is_active")
  createdAt      DateTime         @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt      DateTime         @updatedAt @map("updated_at") @db.Timestamptz(6)
  assignments    TaskAssignment[]
  congregation   Congregation     @relation(fields: [congregationId], references: [id], onDelete: Cascade)

  @@index([congregationId, category])
  @@index([congregationId, isActive])
  @@map("tasks")
}

model TaskAssignment {
  id               String       @id @default(cuid())
  congregationId   String       @map("congregation_id") @db.VarChar(8)
  taskId           String       @map("task_id")
  assignedMemberId String?      @map("assigned_member_id")
  assignedDate     DateTime     @map("assigned_date") @db.Date
  dueDate          DateTime?    @map("due_date") @db.Date
  status           String       @default("pending") @db.VarChar(50)
  notes            String?
  completedAt      DateTime?    @map("completed_at") @db.Timestamptz(6)
  createdAt        DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt        DateTime     @updatedAt @map("updated_at") @db.Timestamptz(6)
  assignedMember   Member?      @relation(fields: [assignedMemberId], references: [id])
  congregation     Congregation @relation(fields: [congregationId], references: [id], onDelete: Cascade)
  task             Task         @relation(fields: [taskId], references: [id], onDelete: Cascade)

  @@index([congregationId, assignedDate])
  @@index([assignedMemberId, status])
  @@map("task_assignments")
}

model FieldServiceRecord {
  id             String       @id @default(cuid())
  congregationId String       @map("congregation_id") @db.VarChar(8)
  memberId       String       @map("member_id")
  serviceMonth   DateTime     @map("service_month") @db.Date
  hours          Decimal?     @db.Decimal(5, 2)
  placements     Int?         @default(0)
  videoShowings  Int?         @default(0) @map("video_showings")
  returnVisits   Int?         @default(0) @map("return_visits")
  bibleStudies   Int?         @default(0) @map("bible_studies")
  notes          String?
  isSubmitted    Boolean      @default(false) @map("is_submitted")
  submittedAt    DateTime?    @map("submitted_at") @db.Timestamptz(6)
  createdAt      DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt      DateTime     @updatedAt @map("updated_at") @db.Timestamptz(6)
  congregation   Congregation @relation(fields: [congregationId], references: [id], onDelete: Cascade)
  member         Member       @relation(fields: [memberId], references: [id], onDelete: Cascade)

  @@unique([memberId, serviceMonth])
  @@index([congregationId, serviceMonth])
  @@map("field_service_records")
}

model Letter {
  id             String              @id @default(cuid())
  congregationId String              @map("congregation_id") @db.VarChar(8)
  title          String              @db.VarChar(255)
  filename       String              @db.VarChar(255)
  filePath       String              @map("file_path")
  fileSize       Int?                @map("file_size")
  mimeType       String?             @map("mime_type") @db.VarChar(100)
  category       String?             @db.VarChar(100)
  visibility     String              @default("ALL_MEMBERS") @db.VarChar(50)
  uploadDate     DateTime            @default(now()) @map("upload_date") @db.Date
  uploadedById   String?             @map("uploaded_by_id")
  isActive       Boolean             @default(true) @map("is_active")
  createdAt      DateTime            @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt      DateTime            @updatedAt @map("updated_at") @db.Timestamptz(6)
  approvedAt     DateTime?           @map("approved_at") @db.Timestamptz(6)
  approvedById   String?             @map("approved_by_id")
  description    String?
  downloadCount  Int                 @default(0) @map("download_count")
  expirationDate DateTime?           @map("expiration_date") @db.Date
  folderId       String?             @map("folder_id")
  parentId       String?             @map("parent_id")
  priority       String              @default("NORMAL") @db.VarChar(20)
  publishDate    DateTime?           @map("publish_date") @db.Date
  searchableText String?             @map("searchable_text")
  status         String              @default("ACTIVE") @db.VarChar(20)
  subcategory    String?             @db.VarChar(100)
  tags           String[]            @default([])
  thumbnailPath  String?             @map("thumbnail_path")
  version        Int                 @default(1)
  viewCount      Int                 @default(0) @map("view_count")
  accessLogs     DocumentAccessLog[]
  comments       DocumentComment[]
  workflows      DocumentWorkflow[]
  approvedBy     Member?             @relation("ApprovedBy", fields: [approvedById], references: [id])
  congregation   Congregation        @relation(fields: [congregationId], references: [id], onDelete: Cascade)
  folder         DocumentFolder?     @relation(fields: [folderId], references: [id])
  parent         Letter?             @relation("DocumentVersions", fields: [parentId], references: [id])
  versions       Letter[]            @relation("DocumentVersions")
  uploadedBy     Member?             @relation("UploadedBy", fields: [uploadedById], references: [id])

  @@index([congregationId, visibility])
  @@index([congregationId, category])
  @@index([congregationId, status])
  @@index([congregationId, folderId])
  @@index([congregationId, uploadDate])
  @@index([congregationId, expirationDate])
  @@index([title, description, searchableText])
  @@map("letters")
}

model DocumentFolder {
  id             String           @id @default(cuid())
  congregationId String           @map("congregation_id") @db.VarChar(8)
  name           String           @db.VarChar(255)
  description    String?
  parentId       String?          @map("parent_id")
  path           String
  color          String?          @db.VarChar(20)
  icon           String?          @db.VarChar(50)
  visibility     String           @default("ALL_MEMBERS") @db.VarChar(50)
  sortOrder      Int              @default(0) @map("sort_order")
  isActive       Boolean          @default(true) @map("is_active")
  createdAt      DateTime         @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt      DateTime         @updatedAt @map("updated_at") @db.Timestamptz(6)
  congregation   Congregation     @relation(fields: [congregationId], references: [id], onDelete: Cascade)
  parent         DocumentFolder?  @relation("FolderHierarchy", fields: [parentId], references: [id])
  children       DocumentFolder[] @relation("FolderHierarchy")
  documents      Letter[]

  @@index([congregationId, parentId])
  @@index([congregationId, path])
  @@map("document_folders")
}

model DocumentAccessLog {
  id         String   @id @default(cuid())
  documentId String   @map("document_id")
  memberId   String   @map("member_id")
  accessType String   @db.VarChar(20)
  ipAddress  String?  @map("ip_address") @db.VarChar(45)
  userAgent  String?  @map("user_agent")
  accessedAt DateTime @default(now()) @map("accessed_at") @db.Timestamptz(6)
  document   Letter   @relation(fields: [documentId], references: [id], onDelete: Cascade)
  member     Member   @relation(fields: [memberId], references: [id], onDelete: Cascade)

  @@index([documentId, accessedAt])
  @@index([memberId, accessedAt])
  @@map("document_access_logs")
}

model DocumentComment {
  id         String            @id @default(cuid())
  documentId String            @map("document_id")
  memberId   String            @map("member_id")
  parentId   String?           @map("parent_id")
  content    String
  isInternal Boolean           @default(false) @map("is_internal")
  isResolved Boolean           @default(false) @map("is_resolved")
  createdAt  DateTime          @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt  DateTime          @updatedAt @map("updated_at") @db.Timestamptz(6)
  document   Letter            @relation(fields: [documentId], references: [id], onDelete: Cascade)
  member     Member            @relation(fields: [memberId], references: [id], onDelete: Cascade)
  parent     DocumentComment?  @relation("CommentReplies", fields: [parentId], references: [id])
  replies    DocumentComment[] @relation("CommentReplies")

  @@index([documentId, createdAt])
  @@index([memberId, createdAt])
  @@map("document_comments")
}

model DocumentWorkflow {
  id           String    @id @default(cuid())
  documentId   String    @map("document_id")
  workflowType String    @db.VarChar(50)
  status       String    @db.VarChar(20)
  assignedToId String?   @map("assigned_to_id")
  assignedById String    @map("assigned_by_id")
  priority     String    @default("NORMAL") @db.VarChar(20)
  dueDate      DateTime? @map("due_date") @db.Date
  comments     String?
  completedAt  DateTime? @map("completed_at") @db.Timestamptz(6)
  createdAt    DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt    DateTime  @updatedAt @map("updated_at") @db.Timestamptz(6)
  assignedBy   Member    @relation("WorkflowAssignedBy", fields: [assignedById], references: [id], onDelete: Cascade)
  assignedTo   Member?   @relation("WorkflowAssignedTo", fields: [assignedToId], references: [id])
  document     Letter    @relation(fields: [documentId], references: [id], onDelete: Cascade)

  @@index([documentId, status])
  @@index([assignedToId, status])
  @@index([dueDate])
  @@map("document_workflows")
}

model Event {
  id             String       @id @default(cuid())
  congregationId String       @map("congregation_id") @db.VarChar(8)
  title          String       @db.VarChar(255)
  description    String?
  eventDate      DateTime     @map("event_date") @db.Date
  startTime      String?      @map("start_time") @db.VarChar(10)
  endTime        String?      @map("end_time") @db.VarChar(10)
  location       String?      @db.VarChar(255)
  category       String       @db.VarChar(100)
  isAllDay       Boolean      @default(false) @map("is_all_day")
  isRecurring    Boolean      @default(false) @map("is_recurring")
  recurrenceRule String?      @map("recurrence_rule")
  visibility     String       @default("ALL_MEMBERS") @db.VarChar(50)
  isActive       Boolean      @default(true) @map("is_active")
  createdAt      DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt      DateTime     @updatedAt @map("updated_at") @db.Timestamptz(6)
  congregation   Congregation @relation(fields: [congregationId], references: [id], onDelete: Cascade)

  @@index([congregationId, eventDate])
  @@index([congregationId, category])
  @@map("events")
}

model Notification {
  id             String       @id @default(cuid())
  congregationId String       @map("congregation_id") @db.VarChar(8)
  recipientId    String       @map("recipient_id")
  senderId       String?      @map("sender_id")
  title          String       @db.VarChar(255)
  message        String
  category       String       @db.VarChar(50)
  priority       String       @default("NORMAL") @db.VarChar(20)
  deliveryMethod String[]     @map("delivery_method")
  status         String       @default("DELIVERED") @db.VarChar(20)
  scheduledFor   DateTime?    @map("scheduled_for") @db.Timestamptz(6)
  deliveredAt    DateTime?    @map("delivered_at") @db.Timestamptz(6)
  readAt         DateTime?    @map("read_at") @db.Timestamptz(6)
  metadata       Json?        @db.Json
  isActive       Boolean      @default(true) @map("is_active")
  createdAt      DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt      DateTime     @updatedAt @map("updated_at") @db.Timestamptz(6)
  congregation   Congregation @relation(fields: [congregationId], references: [id], onDelete: Cascade)
  recipient      Member       @relation("NotificationRecipient", fields: [recipientId], references: [id], onDelete: Cascade)
  sender         Member?      @relation("NotificationSender", fields: [senderId], references: [id])

  @@index([congregationId, recipientId, readAt])
  @@index([congregationId, category])
  @@index([congregationId, status])
  @@index([congregationId, scheduledFor])
  @@map("notifications")
}

model CommunicationPreference {
  id                  String       @id @default(cuid())
  congregationId      String       @map("congregation_id") @db.VarChar(8)
  memberId            String       @map("member_id")
  emailNotifications  Boolean      @default(true) @map("email_notifications")
  smsNotifications    Boolean      @default(false) @map("sms_notifications")
  inAppNotifications  Boolean      @default(true) @map("in_app_notifications")
  quietHoursStart     String?      @map("quiet_hours_start") @db.VarChar(10)
  quietHoursEnd       String?      @map("quiet_hours_end") @db.VarChar(10)
  categoryPreferences Json         @default("{}") @map("category_preferences") @db.Json
  createdAt           DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt           DateTime     @updatedAt @map("updated_at") @db.Timestamptz(6)
  congregation        Congregation @relation(fields: [congregationId], references: [id], onDelete: Cascade)
  member              Member       @relation(fields: [memberId], references: [id], onDelete: Cascade)

  @@unique([congregationId, memberId], name: "congregationId_memberId")
  @@index([congregationId, memberId])
  @@map("communication_preferences")
}

model CommunicationTemplate {
  id             String       @id @default(cuid())
  congregationId String       @map("congregation_id") @db.VarChar(8)
  name           String       @db.VarChar(255)
  title          String       @db.VarChar(255)
  message        String
  category       String       @db.VarChar(50)
  variables      String[]     @default([])
  isActive       Boolean      @default(true) @map("is_active")
  createdAt      DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt      DateTime     @updatedAt @map("updated_at") @db.Timestamptz(6)
  congregation   Congregation @relation(fields: [congregationId], references: [id], onDelete: Cascade)

  @@index([congregationId, category])
  @@index([congregationId, name])
  @@map("communication_templates")
}

model Song {
  id         String   @id @default(cuid())
  songNumber Int      @unique @map("song_number")
  titleEs    String?  @map("title_es") @db.VarChar(255)
  titleEn    String?  @map("title_en") @db.VarChar(255)
  category   String?  @db.VarChar(100)
  isActive   Boolean  @default(true) @map("is_active")
  createdAt  DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt  DateTime @updatedAt @map("updated_at") @db.Timestamptz(6)

  @@map("songs")
}

model SpecialSong {
  id        String   @id @default(cuid())
  keyName   String   @unique @map("key_name") @db.VarChar(50)
  titleEs   String   @map("title_es") @db.VarChar(255)
  titleEn   String?  @map("title_en") @db.VarChar(255)
  isCustom  Boolean  @default(false) @map("is_custom")
  isActive  Boolean  @default(true) @map("is_active")
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt DateTime @updatedAt @map("updated_at") @db.Timestamptz(6)

  @@map("special_songs")
}

model CongregationSetting {
  id             String       @id @default(cuid())
  congregationId String       @map("congregation_id") @db.VarChar(8)
  settingKey     String       @map("setting_key") @db.VarChar(100)
  settingValue   String?      @map("setting_value")
  createdAt      DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt      DateTime     @updatedAt @map("updated_at") @db.Timestamptz(6)
  congregation   Congregation @relation(fields: [congregationId], references: [id], onDelete: Cascade)

  @@unique([congregationId, settingKey])
  @@index([congregationId, settingKey])
  @@map("congregation_settings")
}

model ServiceGroup {
  id             String            @id @default(cuid())
  congregationId String            @map("congregation_id") @db.VarChar(8)
  name           String            @db.VarChar(100)
  groupNumber    Int               @map("group_number")
  description    String?
  overseerId     String?           @map("overseer_id")
  assistantId    String?           @map("assistant_id")
  address        String?           @db.Text
  isActive       Boolean           @default(true) @map("is_active")
  createdAt      DateTime          @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt      DateTime          @updatedAt @map("updated_at") @db.Timestamptz(6)
  congregation   Congregation      @relation(fields: [congregationId], references: [id], onDelete: Cascade)
  overseer       Member?           @relation("ServiceGroupOverseer", fields: [overseerId], references: [id])
  assistant      Member?           @relation("ServiceGroupAssistant", fields: [assistantId], references: [id])
  assignments    GroupAssignment[]

  @@unique([congregationId, groupNumber])
  @@index([congregationId, isActive])
  @@index([overseerId])
  @@index([assistantId])
  @@map("service_groups")
}

model Territory {
  id               String                @id @default(cuid())
  congregationId   String                @map("congregation_id") @db.VarChar(8)
  territoryNumber  String                @map("territory_number") @db.VarChar(50)
  address          String                @db.Text
  status           TerritoryStatus       @default(available)
  boundaries       Json?                 @db.Json
  notes            String?               @db.Text
  displayOrder     Int?                  @map("display_order")
  createdAt        DateTime              @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt        DateTime              @updatedAt @map("updated_at") @db.Timestamptz(6)
  congregation     Congregation          @relation(fields: [congregationId], references: [id], onDelete: Cascade)
  assignments      TerritoryAssignment[]

  @@unique([territoryNumber, congregationId])
  @@index([congregationId])
  @@index([congregationId, status])
  @@index([displayOrder])
  @@map("territories")
}

model TerritoryAssignment {
  id             String           @id @default(cuid())
  territoryId    String           @map("territory_id")
  memberId       String           @map("member_id")
  assignedBy     String           @map("assigned_by")
  assignedAt     DateTime         @default(now()) @map("assigned_at") @db.Timestamptz(6)
  completedAt    DateTime?        @map("completed_at") @db.Timestamptz(6)
  dueDate        DateTime?        @map("due_date") @db.Timestamptz(6)
  status         AssignmentStatus @default(active)
  notes          String?          @db.Text
  visitCount     Int              @default(0) @map("visit_count")
  isPartiallyCompleted Boolean    @default(false) @map("is_partially_completed")
  partialCompletionNotes String? @db.Text @map("partial_completion_notes")
  congregationId String           @map("congregation_id") @db.VarChar(8)
  createdAt      DateTime         @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt      DateTime         @updatedAt @map("updated_at") @db.Timestamptz(6)
  territory      Territory        @relation(fields: [territoryId], references: [id], onDelete: Cascade)
  member         Member           @relation("TerritoryAssignmentMember", fields: [memberId], references: [id], onDelete: Cascade)
  assignedByMember Member         @relation("TerritoryAssignmentAssignedBy", fields: [assignedBy], references: [id], onDelete: Restrict)
  congregation   Congregation     @relation(fields: [congregationId], references: [id], onDelete: Cascade)
  visits         TerritoryVisit[]

  @@index([congregationId])
  @@index([territoryId])
  @@index([memberId])
  @@index([congregationId, status])
  @@map("territory_assignments")
}

model TerritoryVisit {
  id             String           @id @default(cuid())
  assignmentId   String           @map("assignment_id")
  visitDate      DateTime         @map("visit_date") @db.Timestamptz(6)
  isCompleted    Boolean          @default(false) @map("is_completed")
  notes          String?          @db.Text
  addressesWorked String?         @db.Text @map("addresses_worked")
  congregationId String           @map("congregation_id") @db.VarChar(8)
  createdAt      DateTime         @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt      DateTime         @updatedAt @map("updated_at") @db.Timestamptz(6)

  assignment     TerritoryAssignment @relation(fields: [assignmentId], references: [id], onDelete: Cascade)
  congregation   Congregation     @relation(fields: [congregationId], references: [id], onDelete: Cascade)

  @@index([congregationId])
  @@index([assignmentId])
  @@index([visitDate])
  @@map("territory_visits")
}

model ServiceSchedule {
  id            String                @id @default(cuid())
  congregationId String               @map("congregation_id") @db.VarChar(8)
  weekStartDate DateTime              @map("week_start_date") @db.Date
  weekEndDate   DateTime              @map("week_end_date") @db.Date
  isActive      Boolean               @default(true) @map("is_active")
  createdAt     DateTime              @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt     DateTime              @updatedAt @map("updated_at") @db.Timestamptz(6)
  congregation  Congregation          @relation(fields: [congregationId], references: [id], onDelete: Cascade)
  scheduleTimes ServiceScheduleTime[]

  @@unique([congregationId, weekStartDate])
  @@index([congregationId, weekStartDate])
  @@map("service_schedules")
}

model ServiceScheduleTime {
  id          String          @id @default(cuid())
  scheduleId  String          @map("schedule_id")
  serviceDate DateTime        @map("service_date") @db.Date
  serviceTime String          @map("service_time") @db.VarChar(10)
  location    String          @db.VarChar(255)
  address     String?
  conductorId String?         @map("conductor_id")
  notes       String?
  isActive    Boolean         @default(true) @map("is_active")
  createdAt   DateTime        @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt   DateTime        @updatedAt @map("updated_at") @db.Timestamptz(6)
  schedule    ServiceSchedule @relation(fields: [scheduleId], references: [id], onDelete: Cascade)
  conductor   Member?         @relation(fields: [conductorId], references: [id])

  @@index([scheduleId, serviceDate])
  @@index([conductorId])
  @@map("service_schedule_times")
}

model GroupAssignment {
  id         String       @id @default(cuid())
  groupId    String       @map("group_id")
  memberId   String       @map("member_id")
  role       String       @default("member") @db.VarChar(50)
  assignedAt DateTime     @default(now()) @map("assigned_at") @db.Timestamptz(6)
  isActive   Boolean      @default(true) @map("is_active")
  group      ServiceGroup @relation(fields: [groupId], references: [id], onDelete: Cascade)
  member     Member       @relation(fields: [memberId], references: [id], onDelete: Cascade)

  @@unique([groupId, memberId])
  @@index([groupId, isActive])
  @@index([memberId, isActive])
  @@map("group_assignments")
}
