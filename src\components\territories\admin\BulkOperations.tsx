'use client';

import React, { useState, useEffect } from 'react';
import {
  CheckIcon,
  XMarkIcon,
  UserIcon,
  MapPinIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon,
  CheckCircleIcon,
  XCircleIcon,
  PauseCircleIcon
} from '@heroicons/react/24/outline';

interface Territory {
  id: string;
  territoryNumber: string;
  address: string;
  status: 'available' | 'assigned' | 'completed' | 'out_of_service';
  assignedMember?: {
    id: string;
    name: string;
    assignedAt: string;
  };
}

interface Member {
  id: string;
  name: string;
  role: string;
  activeAssignments: number;
}

interface BulkOperationResult {
  success: boolean;
  territoryId: string;
  territoryNumber: string;
  error?: string;
}

export default function BulkOperations() {
  const [territories, setTerritories] = useState<Territory[]>([]);
  const [members, setMembers] = useState<Member[]>([]);
  const [selectedTerritories, setSelectedTerritories] = useState<Set<string>>(new Set());
  const [selectedMember, setSelectedMember] = useState<string>('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [operationType, setOperationType] = useState<'assign' | 'status' | 'unassign'>('assign');
  const [isLoading, setIsLoading] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [operationResults, setOperationResults] = useState<BulkOperationResult[]>([]);
  const [showResults, setShowResults] = useState(false);

  // Function to get status icon and color
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'available':
        return { icon: CheckCircleIcon, color: 'text-green-600', bgColor: 'bg-green-100' };
      case 'assigned':
        return { icon: UserIcon, color: 'text-blue-600', bgColor: 'bg-blue-100' };
      case 'completed':
        return { icon: CheckIcon, color: 'text-purple-600', bgColor: 'bg-purple-100' };
      case 'out_of_service':
        return { icon: XCircleIcon, color: 'text-red-600', bgColor: 'bg-red-100' };
      default:
        return { icon: PauseCircleIcon, color: 'text-gray-600', bgColor: 'bg-gray-100' };
    }
  };

  useEffect(() => {
    fetchTerritories();
    fetchMembers();
  }, []);

  const fetchTerritories = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');
      if (!token) return;

      const response = await fetch('/api/territories', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setTerritories(data.territories || []);
      }
    } catch (error) {
      console.error('Error fetching territories:', error);
    }
  };

  const fetchMembers = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');
      if (!token) return;

      const response = await fetch('/api/territories/assignments?type=overview', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        const allMembers = data.data?.members || [];

        // Sort members by role hierarchy: Elders, Ministerial Servants, Publishers
        const sortedMembers = allMembers.sort((a, b) => {
          const roleOrder = {
            'elder': 1,
            'overseer_coordinator': 1,
            'coordinator': 1,
            'ministerial_servant': 2,
            'publisher': 3,
            'developer': 0 // Developers first for admin purposes
          };

          const aOrder = roleOrder[a.role] || 4;
          const bOrder = roleOrder[b.role] || 4;

          if (aOrder !== bOrder) {
            return aOrder - bOrder;
          }

          // Within same role, sort alphabetically by name
          return a.name.localeCompare(b.name);
        });

        setMembers(sortedMembers);
      }
    } catch (error) {
      console.error('Error fetching members:', error);
    }
  };

  const toggleTerritorySelection = (territoryId: string) => {
    const newSelection = new Set(selectedTerritories);
    if (newSelection.has(territoryId)) {
      newSelection.delete(territoryId);
    } else {
      newSelection.add(territoryId);
    }
    setSelectedTerritories(newSelection);
  };

  const selectAllTerritories = () => {
    const filteredTerritories = getFilteredTerritories();
    setSelectedTerritories(new Set(filteredTerritories.map(t => t.id)));
  };

  const clearSelection = () => {
    setSelectedTerritories(new Set());
  };

  const getFilteredTerritories = () => {
    switch (operationType) {
      case 'assign':
        return territories.filter(t => t.status === 'available');
      case 'unassign':
        return territories.filter(t => t.status === 'assigned');
      case 'status':
        return territories;
      default:
        return territories;
    }
  };

  const handleBulkOperation = async () => {
    setIsLoading(true);
    let results: BulkOperationResult[] = [];

    try {
      const token = localStorage.getItem('hermanos_token');
      if (!token) throw new Error('No authentication token');

      let bulkResponse;

      // Use bulk operations API
      switch (operationType) {
        case 'assign':
          bulkResponse = await fetch('/api/territories/bulk-operations', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              operation: 'bulk_assign',
              territoryIds: Array.from(selectedTerritories),
              memberId: selectedMember
            })
          });
          break;

        case 'unassign':
          bulkResponse = await fetch('/api/territories/bulk-operations', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              operation: 'bulk_unassign',
              territoryIds: Array.from(selectedTerritories)
            })
          });
          break;

        case 'status':
          bulkResponse = await fetch('/api/territories/bulk-operations', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              operation: 'bulk_status_update',
              territoryIds: Array.from(selectedTerritories),
              status: selectedStatus
            })
          });
          break;
      }

      if (bulkResponse && bulkResponse.ok) {
        const bulkData = await bulkResponse.json();

        // Process bulk results
        results = bulkData.results.map((result: any) => {
          const territory = territories.find(t => t.id === result.territoryId);
          return {
            success: result.success,
            territoryId: result.territoryId,
            territoryNumber: territory?.territoryNumber || 'Unknown',
            error: result.error
          };
        });
      } else {
        const errorData = await bulkResponse?.json().catch(() => ({}));
        throw new Error(errorData.error || 'Bulk operation failed');
      }

      setOperationResults(results);
      setShowResults(true);
      setShowConfirmation(false);

      // Refresh territories
      await fetchTerritories();
      await fetchMembers();

      // Clear selection
      clearSelection();

    } catch (error) {
      console.error('Bulk operation error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filteredTerritories = getFilteredTerritories();
  const selectedTerritoriesData = territories.filter(t => selectedTerritories.has(t.id));

  return (
    <div className="p-4 space-y-6">
      {/* Operation Type Selection */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Tipo de Operación</h3>

        {/* Desktop Layout */}
        <div className="hidden md:grid md:grid-cols-3 gap-4">
          <button
            onClick={() => {
              setOperationType('assign');
              clearSelection();
            }}
            className={`p-4 rounded-lg border-2 transition-colors ${
              operationType === 'assign'
                ? 'border-blue-500 bg-blue-50 text-blue-700'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <UserIcon className="w-8 h-8 mx-auto mb-2" />
            <div className="font-medium">Asignar Territorios</div>
            <div className="text-sm text-gray-600">Asignar múltiples territorios a un hermano</div>
          </button>

          <button
            onClick={() => {
              setOperationType('unassign');
              clearSelection();
            }}
            className={`p-4 rounded-lg border-2 transition-colors ${
              operationType === 'unassign'
                ? 'border-red-500 bg-red-50 text-red-700'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <XMarkIcon className="w-8 h-8 mx-auto mb-2" />
            <div className="font-medium">Desasignar Territorios</div>
            <div className="text-sm text-gray-600">Desasignar múltiples territorios</div>
          </button>

          <button
            onClick={() => {
              setOperationType('status');
              clearSelection();
            }}
            className={`p-4 rounded-lg border-2 transition-colors ${
              operationType === 'status'
                ? 'border-green-500 bg-green-50 text-green-700'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <ArrowPathIcon className="w-8 h-8 mx-auto mb-2" />
            <div className="font-medium">Cambiar Estado</div>
            <div className="text-sm text-gray-600">Cambiar estado de múltiples territorios</div>
          </button>
        </div>

        {/* Mobile Layout - 3 columns, icons only with short text */}
        <div className="md:hidden grid grid-cols-3 gap-2">
          <button
            onClick={() => {
              setOperationType('assign');
              clearSelection();
            }}
            className={`p-3 rounded-lg border-2 transition-colors ${
              operationType === 'assign'
                ? 'border-blue-500 bg-blue-50 text-blue-700'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <UserIcon className="w-6 h-6 mx-auto mb-1" />
            <div className="text-xs font-medium text-center">Asignar</div>
          </button>

          <button
            onClick={() => {
              setOperationType('unassign');
              clearSelection();
            }}
            className={`p-3 rounded-lg border-2 transition-colors ${
              operationType === 'unassign'
                ? 'border-red-500 bg-red-50 text-red-700'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <XMarkIcon className="w-6 h-6 mx-auto mb-1" />
            <div className="text-xs font-medium text-center">Desasignar</div>
          </button>

          <button
            onClick={() => {
              setOperationType('status');
              clearSelection();
            }}
            className={`p-3 rounded-lg border-2 transition-colors ${
              operationType === 'status'
                ? 'border-green-500 bg-green-50 text-green-700'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <ArrowPathIcon className="w-6 h-6 mx-auto mb-1" />
            <div className="text-xs font-medium text-center">Cambiar</div>
          </button>
        </div>
      </div>

      {/* Operation Configuration */}
      {operationType === 'assign' && (
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Seleccionar Hermano</h3>
          <select
            value={selectedMember}
            onChange={(e) => setSelectedMember(e.target.value)}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Seleccionar hermano...</option>
            {members.map((member) => (
              <option key={member.id} value={member.id}>
                {member.name}
              </option>
            ))}
          </select>
        </div>
      )}

      {operationType === 'status' && (
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Nuevo Estado</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {[
              { value: 'available', label: 'Disponible', icon: CheckCircleIcon, color: 'text-green-600', bgColor: 'bg-green-100', borderColor: 'border-green-300' },
              { value: 'assigned', label: 'Asignado', icon: UserIcon, color: 'text-blue-600', bgColor: 'bg-blue-100', borderColor: 'border-blue-300' },
              { value: 'completed', label: 'Completado', icon: CheckIcon, color: 'text-purple-600', bgColor: 'bg-purple-100', borderColor: 'border-purple-300' },
              { value: 'out_of_service', label: 'Fuera de Servicio', icon: XCircleIcon, color: 'text-red-600', bgColor: 'bg-red-100', borderColor: 'border-red-300' }
            ].map((status) => {
              const StatusIcon = status.icon;
              return (
                <button
                  key={status.value}
                  onClick={() => setSelectedStatus(status.value)}
                  className={`p-3 rounded-lg border-2 transition-colors ${
                    selectedStatus === status.value
                      ? `${status.borderColor} ${status.bgColor} ${status.color}`
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <StatusIcon className={`w-6 h-6 mx-auto mb-2 ${
                    selectedStatus === status.value ? status.color : 'text-gray-400'
                  }`} />
                  <div className={`text-sm font-medium ${
                    selectedStatus === status.value ? status.color : 'text-gray-600'
                  }`}>
                    {status.label}
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      )}

      {/* Territory Selection */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">
              Seleccionar Territorios ({selectedTerritories.size} seleccionados)
            </h3>
            <div className="flex space-x-2">
              <button
                onClick={selectAllTerritories}
                className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200"
              >
                Seleccionar Todos
              </button>
              <button
                onClick={clearSelection}
                className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
              >
                Limpiar
              </button>
            </div>
          </div>
        </div>

        <div className="max-h-96 overflow-y-auto">
          {filteredTerritories.map((territory) => {
            const statusInfo = getStatusIcon(territory.status);
            const StatusIcon = statusInfo.icon;

            return (
              <div
                key={territory.id}
                className={`p-3 border-b border-gray-100 cursor-pointer transition-colors ${
                  selectedTerritories.has(territory.id) ? 'bg-blue-50' : 'hover:bg-gray-50'
                }`}
                onClick={() => toggleTerritorySelection(territory.id)}
              >
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={selectedTerritories.has(territory.id)}
                    onChange={() => toggleTerritorySelection(territory.id)}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <div className="flex items-center space-x-3 flex-1">
                    <span className="font-medium text-gray-900 text-lg">
                      {territory.territoryNumber}
                    </span>
                    <div className={`p-1.5 rounded-full ${statusInfo.bgColor}`}>
                      <StatusIcon className={`w-4 h-4 ${statusInfo.color}`} />
                    </div>
                    {territory.assignedMember && (
                      <div className="text-sm text-blue-600 flex items-center space-x-1">
                        <UserIcon className="w-4 h-4" />
                        <span>{territory.assignedMember.name}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Action Button */}
      {selectedTerritories.size > 0 && (
        <div className="flex justify-end">
          <button
            onClick={() => setShowConfirmation(true)}
            disabled={
              (operationType === 'assign' && !selectedMember) ||
              (operationType === 'status' && !selectedStatus)
            }
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Ejecutar Operación ({selectedTerritories.size} territorios)
          </button>
        </div>
      )}

      {/* Confirmation Modal */}
      {showConfirmation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center space-x-3 mb-4">
              <ExclamationTriangleIcon className="w-6 h-6 text-yellow-600" />
              <h3 className="text-lg font-semibold text-gray-900">Confirmar Operación</h3>
            </div>

            <div className="mb-4">
              <p className="text-gray-600 mb-2">
                ¿Está seguro de que desea ejecutar esta operación en {selectedTerritories.size} territorios?
              </p>

              {operationType === 'assign' && selectedMember && (
                <p className="text-sm text-blue-600">
                  Asignar a: {members.find(m => m.id === selectedMember)?.name}
                </p>
              )}

              {operationType === 'status' && selectedStatus && (
                <p className="text-sm text-green-600">
                  Cambiar estado a: {selectedStatus === 'available' ? 'Disponible' :
                                   selectedStatus === 'assigned' ? 'Asignado' :
                                   selectedStatus === 'completed' ? 'Completado' :
                                   'Fuera de Servicio'}
                </p>
              )}
            </div>

            <div className="flex space-x-3">
              <button
                onClick={() => setShowConfirmation(false)}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                Cancelar
              </button>
              <button
                onClick={handleBulkOperation}
                disabled={isLoading}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                {isLoading ? 'Procesando...' : 'Confirmar'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Results Modal */}
      {showResults && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Resultados de la Operación</h3>

            <div className="space-y-2 mb-4">
              {operationResults.map((result, index) => (
                <div
                  key={index}
                  className={`p-3 rounded-lg flex items-center space-x-3 ${
                    result.success ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'
                  }`}
                >
                  {result.success ? (
                    <CheckIcon className="w-5 h-5" />
                  ) : (
                    <XMarkIcon className="w-5 h-5" />
                  )}
                  <div className="flex-1">
                    <span className="font-medium">Territorio {result.territoryNumber}</span>
                    {result.error && (
                      <div className="text-sm">{result.error}</div>
                    )}
                  </div>
                </div>
              ))}
            </div>

            <div className="flex justify-between items-center">
              <div className="text-sm text-gray-600">
                Exitosos: {operationResults.filter(r => r.success).length} |
                Fallidos: {operationResults.filter(r => !r.success).length}
              </div>
              <button
                onClick={() => setShowResults(false)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Cerrar
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
