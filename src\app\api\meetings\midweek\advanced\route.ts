/**
 * Advanced Midweek Meeting Management API
 * 
 * Enhanced API endpoints for advanced midweek meeting management with
 * JW.org integration, conflict detection, and bulk assignment capabilities.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { EnhancedJwOrgService, FetchOptions } from '@/lib/services/enhancedJwOrgService';
import { AssignmentConflictService, ConflictDetectionOptions } from '@/lib/services/assignmentConflictService';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Validation schemas
const BulkAssignmentSchema = z.object({
  meetingIds: z.array(z.string()),
  assignmentPattern: z.object({
    partType: z.string(),
    memberIds: z.array(z.string()),
    rotationPattern: z.enum(['sequential', 'random', 'balanced']),
  }),
});

const ConflictDetectionSchema = z.object({
  meetingId: z.string(),
  proposedAssignments: z.array(z.object({
    partId: z.string(),
    memberId: z.string(),
    assistantId: z.string().optional(),
  })),
  options: z.object({
    checkScheduling: z.boolean().default(true),
    checkQualifications: z.boolean().default(true),
    checkAvailability: z.boolean().default(true),
    checkFrequency: z.boolean().default(true),
    checkWorkload: z.boolean().default(true),
    timeWindow: z.number().default(90),
    maxAssignmentsPerMonth: z.number().default(4),
  }).optional(),
});

const JwOrgFetchSchema = z.object({
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  language: z.enum(['es', 'en']).default('es'),
  forceRefresh: z.boolean().default(false),
  useCache: z.boolean().default(true),
  fallbackToLocal: z.boolean().default(true),
});

/**
 * POST /api/meetings/midweek/advanced
 * Handle various advanced operations based on the action parameter
 */
export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check admin permissions
    if (!['developer', 'overseer', 'elder'].includes(member.role)) {
      return NextResponse.json(
        { error: 'Admin permissions required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { action } = body;

    switch (action) {
      case 'bulk_assign':
        return await handleBulkAssignment(body, member.congregationId);
      
      case 'detect_conflicts':
        return await handleConflictDetection(body, member.congregationId);
      
      case 'fetch_jw_org':
        return await handleJwOrgFetch(body, member.congregationId);
      
      case 'clear_cache':
        return await handleClearCache(body);
      
      case 'get_cache_stats':
        return await handleGetCacheStats();
      
      default:
        return NextResponse.json(
          { error: 'Invalid action specified' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Advanced midweek meeting API error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * Handle bulk assignment operations
 */
async function handleBulkAssignment(body: any, congregationId: string) {
  const validationResult = BulkAssignmentSchema.safeParse(body);
  
  if (!validationResult.success) {
    return NextResponse.json(
      {
        error: 'Invalid bulk assignment data',
        details: validationResult.error.errors,
      },
      { status: 400 }
    );
  }

  const { meetingIds, assignmentPattern } = validationResult.data;

  try {
    // Load meetings to verify they exist and belong to the congregation
    const meetings = await prisma.midweekMeeting.findMany({
      where: {
        id: { in: meetingIds },
        congregationId,
      },
      include: {
        parts: true,
      },
    });

    if (meetings.length !== meetingIds.length) {
      return NextResponse.json(
        { error: 'Some meetings not found or access denied' },
        { status: 404 }
      );
    }

    // Apply bulk assignments based on pattern
    const assignments = await applyBulkAssignmentPattern(
      meetings,
      assignmentPattern,
      congregationId
    );

    return NextResponse.json({
      success: true,
      assignmentsCreated: assignments.length,
      assignments,
      message: 'Bulk assignments created successfully',
    });
  } catch (error) {
    console.error('Bulk assignment error:', error);
    return NextResponse.json(
      { error: 'Failed to create bulk assignments' },
      { status: 500 }
    );
  }
}

/**
 * Handle conflict detection
 */
async function handleConflictDetection(body: any, congregationId: string) {
  const validationResult = ConflictDetectionSchema.safeParse(body);
  
  if (!validationResult.success) {
    return NextResponse.json(
      {
        error: 'Invalid conflict detection data',
        details: validationResult.error.errors,
      },
      { status: 400 }
    );
  }

  const { meetingId, proposedAssignments, options } = validationResult.data;

  try {
    // Verify meeting exists and belongs to congregation
    const meeting = await prisma.midweekMeeting.findFirst({
      where: {
        id: meetingId,
        congregationId,
      },
      include: {
        parts: true,
      },
    });

    if (!meeting) {
      return NextResponse.json(
        { error: 'Meeting not found or access denied' },
        { status: 404 }
      );
    }

    // Convert proposed assignments to the format expected by conflict service
    const conflictAssignments = await convertToConflictAssignments(
      proposedAssignments,
      meeting,
      congregationId
    );

    // Detect conflicts
    const conflicts = await AssignmentConflictService.detectConflicts(
      congregationId,
      conflictAssignments,
      options as ConflictDetectionOptions
    );

    return NextResponse.json({
      success: true,
      conflictsFound: conflicts.length,
      conflicts,
      summary: {
        critical: conflicts.filter(c => c.severity === 'critical').length,
        high: conflicts.filter(c => c.severity === 'high').length,
        medium: conflicts.filter(c => c.severity === 'medium').length,
        low: conflicts.filter(c => c.severity === 'low').length,
      },
    });
  } catch (error) {
    console.error('Conflict detection error:', error);
    return NextResponse.json(
      { error: 'Failed to detect conflicts' },
      { status: 500 }
    );
  }
}

/**
 * Handle JW.org data fetching
 */
async function handleJwOrgFetch(body: any, congregationId: string) {
  const validationResult = JwOrgFetchSchema.safeParse(body);
  
  if (!validationResult.success) {
    return NextResponse.json(
      {
        error: 'Invalid JW.org fetch parameters',
        details: validationResult.error.errors,
      },
      { status: 400 }
    );
  }

  const { startDate, endDate, language, forceRefresh, useCache, fallbackToLocal } = validationResult.data;

  try {
    const fetchOptions: FetchOptions = {
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      language,
      retryAttempts: 3,
      timeout: 30000,
      cache: {
        ttl: 86400, // 24 hours
        useCache,
        forceRefresh,
      },
      fallbackToLocal,
    };

    const result = await EnhancedJwOrgService.fetchWorkbookData(fetchOptions);

    return NextResponse.json({
      success: result.success,
      data: result.data,
      errors: result.errors,
      warnings: result.warnings,
      performance: result.performance,
      summary: {
        meetingsFetched: result.data.length,
        dateRange: { startDate, endDate },
        language,
        cacheHits: result.performance.cacheHits,
        cacheMisses: result.performance.cacheMisses,
        fallbacksUsed: result.performance.fallbacksUsed,
        totalTime: `${result.performance.totalTime}ms`,
      },
    });
  } catch (error) {
    console.error('JW.org fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch JW.org data' },
      { status: 500 }
    );
  }
}

/**
 * Handle cache clearing
 */
async function handleClearCache(body: any) {
  const { startDate, endDate, language } = body;

  try {
    await EnhancedJwOrgService.clearCache(
      new Date(startDate),
      new Date(endDate),
      language || 'es'
    );

    return NextResponse.json({
      success: true,
      message: 'Cache cleared successfully',
    });
  } catch (error) {
    console.error('Cache clear error:', error);
    return NextResponse.json(
      { error: 'Failed to clear cache' },
      { status: 500 }
    );
  }
}

/**
 * Handle getting cache statistics
 */
async function handleGetCacheStats() {
  try {
    const stats = await EnhancedJwOrgService.getCacheStats();

    return NextResponse.json({
      success: true,
      stats,
    });
  } catch (error) {
    console.error('Cache stats error:', error);
    return NextResponse.json(
      { error: 'Failed to get cache statistics' },
      { status: 500 }
    );
  }
}

/**
 * Apply bulk assignment pattern to meetings
 */
async function applyBulkAssignmentPattern(
  meetings: any[],
  pattern: any,
  congregationId: string
) {
  const assignments = [];
  let memberIndex = 0;

  for (const meeting of meetings) {
    const relevantParts = meeting.parts.filter((part: any) => 
      part.partType === pattern.partType
    );

    for (const part of relevantParts) {
      let assignedMemberId;

      switch (pattern.rotationPattern) {
        case 'sequential':
          assignedMemberId = pattern.memberIds[memberIndex % pattern.memberIds.length];
          memberIndex++;
          break;
        
        case 'random':
          assignedMemberId = pattern.memberIds[Math.floor(Math.random() * pattern.memberIds.length)];
          break;
        
        case 'balanced':
          // For balanced, we would need to track assignment counts
          // For now, use sequential
          assignedMemberId = pattern.memberIds[memberIndex % pattern.memberIds.length];
          memberIndex++;
          break;
      }

      // Update the part with the assignment
      await prisma.midweekMeetingPart.update({
        where: { id: part.id },
        data: { assignedMember: assignedMemberId },
      });

      assignments.push({
        meetingId: meeting.id,
        partId: part.id,
        memberId: assignedMemberId,
        partType: part.partType,
        partTitle: part.title,
      });
    }
  }

  return assignments;
}

/**
 * Convert proposed assignments to conflict assignment format
 */
async function convertToConflictAssignments(
  proposedAssignments: any[],
  meeting: any,
  congregationId: string
) {
  const conflictAssignments = [];

  for (const assignment of proposedAssignments) {
    const part = meeting.parts.find((p: any) => p.id === assignment.partId);
    if (!part) continue;

    // Load member name (in a real implementation)
    const memberName = `Member ${assignment.memberId}`;
    const assistantName = assignment.assistantId ? `Assistant ${assignment.assistantId}` : undefined;

    conflictAssignments.push({
      meetingId: meeting.id,
      meetingDate: meeting.meetingDate,
      partId: part.id,
      partTitle: part.title,
      memberId: assignment.memberId,
      memberName,
      assistantId: assignment.assistantId,
      assistantName,
    });
  }

  return conflictAssignments;
}

/**
 * GET /api/meetings/midweek/advanced
 * Get advanced meeting analytics and statistics
 */
export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    switch (action) {
      case 'analytics':
        return await getAssignmentAnalytics(member.congregationId, searchParams);
      
      case 'cache_stats':
        return await handleGetCacheStats();
      
      default:
        return NextResponse.json(
          { error: 'Invalid action specified' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Advanced midweek meeting GET error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Get assignment analytics
 */
async function getAssignmentAnalytics(congregationId: string, searchParams: URLSearchParams) {
  const startDate = searchParams.get('startDate');
  const endDate = searchParams.get('endDate');
  const memberId = searchParams.get('memberId');

  try {
    // This would implement comprehensive analytics
    // For now, return basic structure
    const analytics = {
      totalMeetings: 0,
      totalAssignments: 0,
      memberParticipation: {},
      partTypeDistribution: {},
      assignmentFrequency: {},
      conflictHistory: [],
    };

    return NextResponse.json({
      success: true,
      analytics,
      dateRange: { startDate, endDate },
      memberId,
    });
  } catch (error) {
    console.error('Analytics error:', error);
    return NextResponse.json(
      { error: 'Failed to get analytics' },
      { status: 500 }
    );
  }
}
