/**
 * Midweek Meetings Import API Endpoint
 *
 * Handles importing midweek meeting data from JW.org using the existing
 * wol-scraper.js functionality and storing it in the database.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';

// Import the existing wol-scraper functionality
const { fetchMidweekMeetingData } = require('../../../../../../wol-scraper.js');

// Validation schema for import request
const ImportRequestSchema = z.object({
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid start date format (YYYY-MM-DD)'),
  endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid end date format (YYYY-MM-DD)'),
  language: z.string().default('es').optional(),
});

// Interface for import response
interface ImportResponse {
  success: boolean;
  message: string;
  importedCount: number;
  skippedCount: number;
  errorCount: number;
  errors: string[];
  dateRange: {
    startDate: string;
    endDate: string;
  };
}

// Helper function to get week number from date
function getWeekNumber(date: Date): number {
  const startOfYear = new Date(date.getFullYear(), 0, 1);
  const pastDaysOfYear = (date.getTime() - startOfYear.getTime()) / 86400000;
  return Math.ceil((pastDaysOfYear + startOfYear.getDay() + 1) / 7);
}

// Helper function to convert JW.org meeting data to database format
function mapMeetingDataToDatabase(meetingData: any, congregationId: string) {
  const meetingDate = new Date(meetingData.date);

  // Extract meeting parts from sections
  const parts: any[] = [];
  let partNumber = 1;

  // Add treasures section parts
  if (meetingData.sections?.treasures) {
    meetingData.sections.treasures.forEach((part: any) => {
      parts.push({
        partNumber: partNumber++,
        partType: part.part_type || 'treasures',
        title: part.title,
        timeAllocation: part.duration || null,
        notes: part.notes || null,
        isCompleted: false,
      });
    });
  }

  // Add ministry section parts
  if (meetingData.sections?.ministry) {
    meetingData.sections.ministry.forEach((part: any) => {
      parts.push({
        partNumber: partNumber++,
        partType: part.part_type || 'ministry',
        title: part.title,
        timeAllocation: part.duration || null,
        notes: part.notes || null,
        isCompleted: false,
      });
    });
  }

  // Add christian life section parts
  if (meetingData.sections?.christian_life) {
    meetingData.sections.christian_life.forEach((part: any) => {
      parts.push({
        partNumber: partNumber++,
        partType: part.part_type || 'christian_life',
        title: part.title,
        timeAllocation: part.duration || null,
        notes: part.notes || null,
        isCompleted: false,
      });
    });
  }

  return {
    meeting: {
      congregationId,
      meetingDate,
      chairman: null,
      openingPrayer: null,
      closingPrayer: null,
      location: 'Kingdom Hall',
      zoomLink: null,
      notes: meetingData.scripture || null,
      isActive: true,
    },
    parts,
  };
}

export async function POST(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = await extractAndVerifyToken(request);
    if (!authResult.success || !authResult.member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { member } = authResult;

    // Parse and validate request body
    const body = await request.json();
    const validationResult = ImportRequestSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid request parameters',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const { startDate, endDate, language } = validationResult.data;

    // Validate date range
    const start = new Date(startDate);
    const end = new Date(endDate);

    if (start > end) {
      return NextResponse.json(
        { error: 'Start date must be before end date' },
        { status: 400 }
      );
    }

    // Check for reasonable date range (max 6 months)
    const maxRange = 180 * 24 * 60 * 60 * 1000; // 180 days in milliseconds
    if (end.getTime() - start.getTime() > maxRange) {
      return NextResponse.json(
        { error: 'Date range cannot exceed 6 months' },
        { status: 400 }
      );
    }

    // Initialize response tracking
    const response: ImportResponse = {
      success: true,
      message: 'Import completed',
      importedCount: 0,
      skippedCount: 0,
      errorCount: 0,
      errors: [],
      dateRange: { startDate, endDate },
    };

    console.log(`Starting import for congregation ${member.congregationId} from ${startDate} to ${endDate}`);

    // Iterate through the date range week by week
    const currentDate = new Date(start);

    while (currentDate <= end) {
      const year = currentDate.getFullYear();
      const weekNumber = getWeekNumber(currentDate);

      try {
        console.log(`Fetching data for ${year}, week ${weekNumber}...`);

        // Fetch meeting data from JW.org using existing scraper
        const meetingData = await fetchMidweekMeetingData(year, weekNumber);

        if (meetingData) {
          // Map the data to database format
          const { meeting, parts } = mapMeetingDataToDatabase(meetingData, member.congregationId);

          // Use transaction to ensure data consistency
          await prisma.$transaction(async (tx) => {
            // Check if meeting already exists
            const existingMeeting = await tx.midweekMeeting.findFirst({
              where: {
                congregationId: member.congregationId,
                meetingDate: meeting.meetingDate,
              },
            });

            let createdMeeting;
            if (existingMeeting) {
              // Update existing meeting
              createdMeeting = await tx.midweekMeeting.update({
                where: { id: existingMeeting.id },
                data: {
                  notes: meeting.notes,
                  updatedAt: new Date(),
                },
              });
            } else {
              // Create new meeting
              createdMeeting = await tx.midweekMeeting.create({
                data: meeting,
              });
            }

            // Delete existing parts for this meeting and recreate them
            await tx.midweekMeetingPart.deleteMany({
              where: { meetingId: createdMeeting.id },
            });

            // Create new parts
            if (parts.length > 0) {
              await tx.midweekMeetingPart.createMany({
                data: parts.map(part => ({
                  ...part,
                  meetingId: createdMeeting.id,
                })),
              });
            }
          });

          response.importedCount++;
          console.log(`Successfully imported meeting for ${year}, week ${weekNumber}`);
        } else {
          response.skippedCount++;
          console.log(`No data available for ${year}, week ${weekNumber}`);
        }
      } catch (error) {
        response.errorCount++;
        const errorMessage = `Failed to import ${year}, week ${weekNumber}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        response.errors.push(errorMessage);
        console.error(errorMessage);
      }

      // Move to next week
      currentDate.setDate(currentDate.getDate() + 7);
    }

    // Update response message based on results
    if (response.errorCount > 0) {
      response.success = false;
      response.message = `Import completed with ${response.errorCount} errors`;
    } else if (response.skippedCount > 0) {
      response.message = `Import completed successfully. ${response.skippedCount} meetings were skipped (no data available)`;
    }

    console.log(`Import completed: ${response.importedCount} imported, ${response.skippedCount} skipped, ${response.errorCount} errors`);

    return NextResponse.json(response);

  } catch (error) {
    console.error('Import endpoint error:', error);

    return NextResponse.json(
      {
        error: 'Failed to import meeting data',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
