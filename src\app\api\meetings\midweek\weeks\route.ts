/**
 * Midweek Meeting Weeks API Endpoint
 *
 * Provides available weeks for midweek meeting import using the wol-scraper
 * fetchWeeks function to match the original SalonDelReino implementation.
 */

import { NextRequest, NextResponse } from 'next/server';
import { extractAndVerifyToken } from '@/lib/middleware/auth';

// Import the wol-scraper fetchWeeks function
const { fetchWeeks } = require('../../../../../../wol-scraper.js');

export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = await extractAndVerifyToken(request);
    if (!authResult.success || !authResult.member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get year from query parameters (default to current year)
    const { searchParams } = new URL(request.url);
    const yearParam = searchParams.get('year');
    const year = yearParam ? parseInt(yearParam) : new Date().getFullYear();

    // Validate year
    const currentYear = new Date().getFullYear();
    if (year < currentYear - 1 || year > currentYear + 2) {
      return NextResponse.json(
        { error: 'Invalid year. Must be within reasonable range.' },
        { status: 400 }
      );
    }

    console.log(`Fetching weeks for year ${year}...`);

    try {
      // Fetch weeks using the wol-scraper
      const weeks = await fetchWeeks(year);
      
      console.log(`Successfully fetched ${weeks.length} weeks for ${year}`);
      
      return NextResponse.json(weeks);

    } catch (scraperError) {
      console.error('Error fetching weeks from wol-scraper:', scraperError);
      
      // Return fallback weeks if scraper fails
      const fallbackWeeks = generateFallbackWeeks(year);
      
      return NextResponse.json(fallbackWeeks, {
        headers: {
          'X-Fallback': 'true',
          'X-Fallback-Reason': 'Scraper error'
        }
      });
    }

  } catch (error) {
    console.error('Weeks endpoint error:', error);

    return NextResponse.json(
      {
        error: 'Failed to fetch weeks',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * Generate fallback weeks when the scraper is not available
 */
function generateFallbackWeeks(year: number) {
  const weeks = [];
  const monthNames = [
    'enero', 'febrero', 'marzo', 'abril', 'mayo', 'junio',
    'julio', 'agosto', 'septiembre', 'octubre', 'noviembre', 'diciembre'
  ];

  // Generate weeks around current date
  const currentDate = new Date();
  const currentWeek = Math.ceil((currentDate.getTime() - new Date(year, 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000));
  
  // Generate 12 weeks: 2 before current, current, and 9 after
  for (let i = Math.max(1, currentWeek - 2); i <= Math.min(52, currentWeek + 9); i++) {
    // Calculate start date (Monday of the week)
    const startDate = new Date(year, 0, 1 + (i - 1) * 7);
    // Adjust to Monday
    const dayOfWeek = startDate.getDay();
    const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
    startDate.setDate(startDate.getDate() - daysToMonday);
    
    // Calculate end date (Sunday of the week)
    const endDate = new Date(startDate);
    endDate.setDate(startDate.getDate() + 6);

    // Create title in Spanish format
    const title = `Semana del ${startDate.getDate()} de ${monthNames[startDate.getMonth()]} al ${endDate.getDate()} de ${monthNames[endDate.getMonth()]} de ${year}`;
    
    weeks.push({
      week_id: `${year}/${i}`,
      workbook_id: `${year}`,
      title,
      url: `https://wol.jw.org/es/wol/meetings/r4/lp-s/${year}/${i}`,
      start_date: startDate.toISOString().split('T')[0],
      end_date: endDate.toISOString().split('T')[0],
      week_number: i
    });
  }

  return weeks;
}
