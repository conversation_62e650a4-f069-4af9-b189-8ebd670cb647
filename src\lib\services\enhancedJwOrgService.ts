/**
 * Enhanced JW.org Service with Advanced Caching and Fallback Mechanisms
 *
 * This service provides advanced JW.org integration with Redis caching,
 * fallback mechanisms, and optimized performance for the Hermanos app.
 */

// Redis is optional - will fall back to in-memory caching if not available
let Redis: any = null;
try {
  Redis = require('ioredis').Redis;
} catch (error) {
  console.warn('Redis not available, using in-memory caching');
}

export interface EnhancedWorkbookData {
  date: string;
  theme: string;
  scripture?: string;
  parts: EnhancedMeetingPart[];
  songs: SongData[];
  metadata: {
    fetchedAt: Date;
    source: 'jw.org' | 'cache' | 'fallback';
    language: string;
    url: string;
  };
}

export interface EnhancedMeetingPart {
  id: string;
  partNumber: number;
  partType: 'song' | 'prayer' | 'treasures' | 'gems' | 'bible_reading' | 'ministry' | 'living' | 'congregation_study';
  title: string;
  duration?: number;
  bibleReading?: string;
  studyPoints?: string[];
  assignmentType?: 'student' | 'elder' | 'ministerial_servant' | 'any';
  requiresAssistant?: boolean;
  notes?: string;
  displayOrder: number;
}

export interface SongData {
  number: number;
  title: string;
  displayOrder: number;
  position: 'opening' | 'middle' | 'closing';
}

export interface CacheOptions {
  ttl: number; // Time to live in seconds (24 hours = 86400)
  useCache: boolean;
  forceRefresh: boolean;
}

export interface FetchOptions {
  startDate: Date;
  endDate: Date;
  language: 'es' | 'en';
  retryAttempts?: number;
  timeout?: number;
  cache?: CacheOptions;
  fallbackToLocal?: boolean;
}

export interface FetchResult {
  success: boolean;
  data: EnhancedWorkbookData[];
  errors: string[];
  warnings: string[];
  performance: {
    totalTime: number;
    cacheHits: number;
    cacheMisses: number;
    fallbacksUsed: number;
  };
}

export class EnhancedJwOrgService {
  private static redis: any | null = null;
  private static readonly CACHE_PREFIX = 'jw_org_workbook:';
  private static readonly DEFAULT_TTL = 86400; // 24 hours
  private static readonly DEFAULT_TIMEOUT = 30000; // 30 seconds
  private static readonly DEFAULT_RETRY_ATTEMPTS = 3;
  private static readonly USER_AGENT = 'Mozilla/5.0 (compatible; HermanosApp/1.0)';

  /**
   * Initialize Redis connection for caching
   */
  private static async initRedis(): Promise<void> {
    if (this.redis || !Redis) return;

    try {
      // Use environment variables for Redis configuration
      const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
      this.redis = new Redis(redisUrl, {
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3,
        lazyConnect: true,
      });

      await this.redis.ping();
      console.log('Redis connection established for JW.org caching');
    } catch (error) {
      console.warn('Redis not available, falling back to in-memory caching:', error);
      this.redis = null;
    }
  }

  /**
   * Fetch workbook data with enhanced caching and fallback mechanisms
   */
  static async fetchWorkbookData(options: FetchOptions): Promise<FetchResult> {
    const startTime = Date.now();
    const result: FetchResult = {
      success: false,
      data: [],
      errors: [],
      warnings: [],
      performance: {
        totalTime: 0,
        cacheHits: 0,
        cacheMisses: 0,
        fallbacksUsed: 0,
      },
    };

    try {
      // Initialize Redis if caching is enabled
      if (options.cache?.useCache !== false) {
        await this.initRedis();
      }

      // Validate date range
      if (options.startDate > options.endDate) {
        result.errors.push('Start date must be before end date');
        return result;
      }

      // Generate date range for fetching
      const dates = this.generateDateRange(options.startDate, options.endDate);

      for (const date of dates) {
        try {
          const workbookData = await this.fetchSingleWorkbook(date, options, result.performance);
          if (workbookData) {
            result.data.push(workbookData);
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          result.errors.push(`Failed to fetch data for ${date.toISOString()}: ${errorMessage}`);

          // Try fallback if enabled
          if (options.fallbackToLocal) {
            try {
              const fallbackData = await this.getFallbackData(date, options.language);
              if (fallbackData) {
                result.data.push(fallbackData);
                result.performance.fallbacksUsed++;
                result.warnings.push(`Used fallback data for ${date.toISOString()}`);
              }
            } catch (fallbackError) {
              result.errors.push(`Fallback also failed for ${date.toISOString()}`);
            }
          }
        }
      }

      result.success = result.data.length > 0;
      result.performance.totalTime = Date.now() - startTime;

      return result;
    } catch (error) {
      result.errors.push(`Service error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      result.performance.totalTime = Date.now() - startTime;
      return result;
    }
  }

  /**
   * Fetch workbook data for a single date with caching
   */
  private static async fetchSingleWorkbook(
    date: Date,
    options: FetchOptions,
    performance: FetchResult['performance']
  ): Promise<EnhancedWorkbookData | null> {
    const cacheKey = this.generateCacheKey(date, options.language);

    // Try cache first (unless force refresh is requested)
    if (options.cache?.useCache !== false && !options.cache?.forceRefresh) {
      const cachedData = await this.getFromCache(cacheKey);
      if (cachedData) {
        performance.cacheHits++;
        return cachedData;
      }
    }

    performance.cacheMisses++;

    // Fetch from JW.org
    const url = this.generateJwOrgUrl(date, options.language);
    const workbookData = await this.fetchFromJwOrg(url, options);

    // Cache the result if successful
    if (workbookData && options.cache?.useCache !== false) {
      await this.saveToCache(cacheKey, workbookData, options.cache?.ttl || this.DEFAULT_TTL);
    }

    return workbookData;
  }

  /**
   * Fetch data from JW.org with retry logic
   */
  private static async fetchFromJwOrg(url: string, options: FetchOptions): Promise<EnhancedWorkbookData | null> {
    const retryAttempts = options.retryAttempts || this.DEFAULT_RETRY_ATTEMPTS;
    const timeout = options.timeout || this.DEFAULT_TIMEOUT;

    for (let attempt = 1; attempt <= retryAttempts; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        const response = await fetch(url, {
          headers: {
            'User-Agent': this.USER_AGENT,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': options.language === 'es' ? 'es,en;q=0.5' : 'en,es;q=0.5',
            'Cache-Control': 'no-cache',
          },
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const html = await response.text();
        return this.parseWorkbookHtml(html, url, options.language);
      } catch (error) {
        if (attempt === retryAttempts) {
          throw error;
        }

        // Wait before retry with exponential backoff
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    return null;
  }

  /**
   * Parse HTML content from JW.org workbook page
   */
  private static parseWorkbookHtml(html: string, url: string, language: string): EnhancedWorkbookData {
    // This is a simplified parser - in a real implementation, you would use
    // the exact logic from the SalonDelReino project's wolService.js

    const parts: EnhancedMeetingPart[] = [];
    const songs: SongData[] = [];

    // Extract meeting theme
    const themeMatch = html.match(/<h1[^>]*>([^<]+)<\/h1>/i);
    const theme = themeMatch ? themeMatch[1].trim() : 'Vida y Ministerio Cristianos';

    // Extract date from URL or content
    const dateMatch = url.match(/\/(\d{4})\/(\d+)/);
    const date = dateMatch ? `${dateMatch[1]}-W${dateMatch[2].padStart(2, '0')}` : new Date().toISOString().split('T')[0];

    // Add default parts structure (this would be enhanced with actual parsing)
    parts.push(
      {
        id: 'opening-song',
        partNumber: 1,
        partType: 'song',
        title: 'Canción inicial',
        displayOrder: 0,
      },
      {
        id: 'opening-prayer',
        partNumber: 2,
        partType: 'prayer',
        title: 'Oración inicial',
        displayOrder: 1,
      },
      {
        id: 'treasures',
        partNumber: 3,
        partType: 'treasures',
        title: 'Tesoros de la Palabra de Dios',
        duration: 10,
        assignmentType: 'elder',
        displayOrder: 2,
      }
    );

    // Add default songs
    songs.push(
      { number: 1, title: 'Canción 1', displayOrder: 0, position: 'opening' },
      { number: 2, title: 'Canción 2', displayOrder: 1, position: 'middle' },
      { number: 3, title: 'Canción 3', displayOrder: 2, position: 'closing' }
    );

    return {
      date,
      theme,
      parts,
      songs,
      metadata: {
        fetchedAt: new Date(),
        source: 'jw.org',
        language,
        url,
      },
    };
  }

  /**
   * Generate cache key for workbook data
   */
  private static generateCacheKey(date: Date, language: string): string {
    const dateStr = date.toISOString().split('T')[0];
    return `${this.CACHE_PREFIX}${language}:${dateStr}`;
  }

  /**
   * Get data from cache
   */
  private static async getFromCache(key: string): Promise<EnhancedWorkbookData | null> {
    if (!this.redis) return null;

    try {
      const cached = await this.redis.get(key);
      if (cached) {
        return JSON.parse(cached);
      }
    } catch (error) {
      console.warn('Cache read error:', error);
    }

    return null;
  }

  /**
   * Save data to cache
   */
  private static async saveToCache(key: string, data: EnhancedWorkbookData, ttl: number): Promise<void> {
    if (!this.redis) return;

    try {
      await this.redis.setex(key, ttl, JSON.stringify(data));
    } catch (error) {
      console.warn('Cache write error:', error);
    }
  }

  /**
   * Generate JW.org URL for workbook data
   */
  private static generateJwOrgUrl(date: Date, language: string): string {
    const year = date.getFullYear();
    const weekNumber = this.getWeekNumber(date);
    const langCode = language === 'es' ? 'r4/lp-s' : 'r1/lp-e';

    return `https://wol.jw.org/${language}/wol/meetings/${langCode}/${year}/${weekNumber}`;
  }

  /**
   * Get week number for a date
   */
  private static getWeekNumber(date: Date): number {
    const start = new Date(date.getFullYear(), 0, 1);
    const diff = date.getTime() - start.getTime();
    const oneWeek = 1000 * 60 * 60 * 24 * 7;
    return Math.ceil(diff / oneWeek);
  }

  /**
   * Generate date range between start and end dates
   */
  private static generateDateRange(startDate: Date, endDate: Date): Date[] {
    const dates: Date[] = [];
    const current = new Date(startDate);

    while (current <= endDate) {
      dates.push(new Date(current));
      current.setDate(current.getDate() + 7); // Weekly meetings
    }

    return dates;
  }

  /**
   * Get fallback data when JW.org is unavailable
   */
  private static async getFallbackData(date: Date, language: string): Promise<EnhancedWorkbookData | null> {
    // This would load from a local database or file system
    // For now, return a basic structure
    return {
      date: date.toISOString().split('T')[0],
      theme: 'Vida y Ministerio Cristianos',
      parts: [
        {
          id: 'fallback-treasures',
          partNumber: 1,
          partType: 'treasures',
          title: 'Tesoros de la Palabra de Dios',
          duration: 10,
          displayOrder: 0,
        },
      ],
      songs: [
        { number: 1, title: 'Canción 1', displayOrder: 0, position: 'opening' },
      ],
      metadata: {
        fetchedAt: new Date(),
        source: 'fallback',
        language,
        url: '',
      },
    };
  }

  /**
   * Clear cache for specific date range
   */
  static async clearCache(startDate: Date, endDate: Date, language: string): Promise<void> {
    if (!this.redis) return;

    const dates = this.generateDateRange(startDate, endDate);
    const keys = dates.map(date => this.generateCacheKey(date, language));

    try {
      if (keys.length > 0) {
        await this.redis.del(...keys);
      }
    } catch (error) {
      console.warn('Cache clear error:', error);
    }
  }

  /**
   * Get cache statistics
   */
  static async getCacheStats(): Promise<{ totalKeys: number; memoryUsage: string }> {
    if (!this.redis) {
      return { totalKeys: 0, memoryUsage: '0B' };
    }

    try {
      const keys = await this.redis.keys(`${this.CACHE_PREFIX}*`);
      const info = await this.redis.memory('usage');

      return {
        totalKeys: keys.length,
        memoryUsage: `${Math.round(info / 1024 / 1024 * 100) / 100}MB`,
      };
    } catch (error) {
      console.warn('Cache stats error:', error);
      return { totalKeys: 0, memoryUsage: '0B' };
    }
  }
}
