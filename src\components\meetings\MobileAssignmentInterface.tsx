'use client';

/**
 * Mobile-Optimized Assignment Interface
 * 
 * Provides a mobile-first interface for managing meeting assignments
 * with touch-friendly controls and optimized layout.
 */

import { useState, useEffect } from 'react';
import {
  UserIcon,
  CalendarIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  PlusIcon,
  XMarkIcon,
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
} from '@heroicons/react/24/outline';

interface Member {
  id: string;
  name: string;
  role: string;
  qualifications: string[];
  availability: boolean;
  assignmentCount: number;
}

interface MeetingPart {
  id: string;
  partType: string;
  title: string;
  duration?: number;
  assignedMember?: Member;
  assistant?: Member;
  requiresAssistant: boolean;
  qualificationRequirements: string[];
  displayOrder: number;
}

interface AssignmentConflict {
  type: 'scheduling' | 'qualification' | 'availability';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
}

interface MobileAssignmentInterfaceProps {
  meetingId: string;
  meetingDate: Date;
  parts: MeetingPart[];
  availableMembers: Member[];
  onAssignmentChange: (partId: string, memberId: string, assistantId?: string) => void;
  onConflictDetected: (conflicts: AssignmentConflict[]) => void;
}

export default function MobileAssignmentInterface({
  meetingId,
  meetingDate,
  parts,
  availableMembers,
  onAssignmentChange,
  onConflictDetected,
}: MobileAssignmentInterfaceProps) {
  const [selectedPart, setSelectedPart] = useState<MeetingPart | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterQualified, setFilterQualified] = useState(false);
  const [showConflicts, setShowConflicts] = useState(false);
  const [conflicts, setConflicts] = useState<AssignmentConflict[]>([]);

  // Filter members based on search and qualifications
  const filteredMembers = availableMembers.filter(member => {
    const matchesSearch = member.name.toLowerCase().includes(searchTerm.toLowerCase());
    
    if (!filterQualified || !selectedPart) {
      return matchesSearch;
    }

    const hasRequiredQualifications = selectedPart.qualificationRequirements.every(req =>
      member.qualifications.includes(req)
    );

    return matchesSearch && hasRequiredQualifications;
  });

  // Detect conflicts when assignments change
  useEffect(() => {
    detectConflicts();
  }, [parts]);

  const detectConflicts = () => {
    const newConflicts: AssignmentConflict[] = [];

    // Check for scheduling conflicts (same member assigned multiple times on same date)
    const memberAssignments = new Map<string, MeetingPart[]>();
    
    parts.forEach(part => {
      if (part.assignedMember) {
        const memberId = part.assignedMember.id;
        if (!memberAssignments.has(memberId)) {
          memberAssignments.set(memberId, []);
        }
        memberAssignments.get(memberId)!.push(part);
      }
    });

    memberAssignments.forEach((assignedParts, memberId) => {
      if (assignedParts.length > 1) {
        newConflicts.push({
          type: 'scheduling',
          severity: 'high',
          message: `${assignedParts[0].assignedMember?.name} está asignado a múltiples partes`,
        });
      }
    });

    // Check for qualification conflicts
    parts.forEach(part => {
      if (part.assignedMember) {
        const hasRequiredQualifications = part.qualificationRequirements.every(req =>
          part.assignedMember!.qualifications.includes(req)
        );

        if (!hasRequiredQualifications) {
          newConflicts.push({
            type: 'qualification',
            severity: 'critical',
            message: `${part.assignedMember.name} no está calificado para "${part.title}"`,
          });
        }
      }
    });

    setConflicts(newConflicts);
    onConflictDetected(newConflicts);
  };

  const handleAssignMember = (memberId: string, isAssistant: boolean = false) => {
    if (!selectedPart) return;

    if (isAssistant) {
      onAssignmentChange(selectedPart.id, selectedPart.assignedMember?.id || '', memberId);
    } else {
      onAssignmentChange(selectedPart.id, memberId);
    }

    setSelectedPart(null);
  };

  const handleRemoveAssignment = (partId: string, isAssistant: boolean = false) => {
    const part = parts.find(p => p.id === partId);
    if (!part) return;

    if (isAssistant) {
      onAssignmentChange(partId, part.assignedMember?.id || '');
    } else {
      onAssignmentChange(partId, '');
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-50 border-red-200';
      case 'high': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getMemberAvailabilityColor = (member: Member) => {
    if (!member.availability) return 'text-red-500';
    if (member.assignmentCount >= 3) return 'text-yellow-500';
    return 'text-green-500';
  };

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">Asignaciones</h2>
            <p className="text-sm text-gray-500">
              {meetingDate.toLocaleDateString('es-ES', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </p>
          </div>
          <button
            onClick={() => setShowConflicts(!showConflicts)}
            className={`p-2 rounded-lg transition-colors ${
              conflicts.length > 0 
                ? 'bg-red-100 text-red-600' 
                : 'bg-gray-100 text-gray-600'
            }`}
          >
            <ExclamationTriangleIcon className="h-5 w-5" />
            {conflicts.length > 0 && (
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                {conflicts.length}
              </span>
            )}
          </button>
        </div>
      </div>

      {/* Conflicts Panel */}
      {showConflicts && conflicts.length > 0 && (
        <div className="bg-white border-b border-gray-200 px-4 py-3">
          <h3 className="text-sm font-medium text-gray-900 mb-2">Conflictos Detectados</h3>
          <div className="space-y-2">
            {conflicts.map((conflict, index) => (
              <div
                key={index}
                className={`p-2 rounded-lg border text-sm ${getSeverityColor(conflict.severity)}`}
              >
                {conflict.message}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Parts List */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4 space-y-3">
          {parts.map((part) => (
            <div
              key={part.id}
              className="bg-white rounded-lg border border-gray-200 p-4"
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">{part.title}</h3>
                  <div className="flex items-center space-x-2 mt-1 text-sm text-gray-500">
                    <span className="capitalize">{part.partType}</span>
                    {part.duration && (
                      <>
                        <span>•</span>
                        <div className="flex items-center space-x-1">
                          <ClockIcon className="h-3 w-3" />
                          <span>{part.duration} min</span>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>

              {/* Main Assignment */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Asignado:</span>
                  {part.assignedMember ? (
                    <div className="flex items-center space-x-2">
                      <div className="flex items-center space-x-2">
                        <UserIcon className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-900">{part.assignedMember.name}</span>
                      </div>
                      <button
                        onClick={() => handleRemoveAssignment(part.id)}
                        className="p-1 text-red-500 hover:bg-red-50 rounded"
                      >
                        <XMarkIcon className="h-4 w-4" />
                      </button>
                    </div>
                  ) : (
                    <button
                      onClick={() => setSelectedPart(part)}
                      className="flex items-center space-x-1 text-blue-600 hover:bg-blue-50 px-2 py-1 rounded"
                    >
                      <PlusIcon className="h-4 w-4" />
                      <span className="text-sm">Asignar</span>
                    </button>
                  )}
                </div>

                {/* Assistant Assignment */}
                {part.requiresAssistant && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">Ayudante:</span>
                    {part.assistant ? (
                      <div className="flex items-center space-x-2">
                        <div className="flex items-center space-x-2">
                          <UserIcon className="h-4 w-4 text-gray-400" />
                          <span className="text-sm text-gray-900">{part.assistant.name}</span>
                        </div>
                        <button
                          onClick={() => handleRemoveAssignment(part.id, true)}
                          className="p-1 text-red-500 hover:bg-red-50 rounded"
                        >
                          <XMarkIcon className="h-4 w-4" />
                        </button>
                      </div>
                    ) : (
                      <button
                        onClick={() => setSelectedPart(part)}
                        className="flex items-center space-x-1 text-blue-600 hover:bg-blue-50 px-2 py-1 rounded"
                      >
                        <PlusIcon className="h-4 w-4" />
                        <span className="text-sm">Asignar Ayudante</span>
                      </button>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Member Selection Modal */}
      {selectedPart && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-end z-50">
          <div className="bg-white w-full max-h-[80vh] rounded-t-xl">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Seleccionar Miembro</h3>
                <p className="text-sm text-gray-500">{selectedPart.title}</p>
              </div>
              <button
                onClick={() => setSelectedPart(null)}
                className="p-2 text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            {/* Search and Filters */}
            <div className="p-4 border-b border-gray-200 space-y-3">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Buscar miembro..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setFilterQualified(!filterQualified)}
                  className={`flex items-center space-x-2 px-3 py-1 rounded-lg text-sm transition-colors ${
                    filterQualified 
                      ? 'bg-blue-100 text-blue-700' 
                      : 'bg-gray-100 text-gray-600'
                  }`}
                >
                  <AdjustmentsHorizontalIcon className="h-4 w-4" />
                  <span>Solo Calificados</span>
                </button>
              </div>
            </div>

            {/* Members List */}
            <div className="flex-1 overflow-y-auto max-h-96">
              <div className="p-4 space-y-2">
                {filteredMembers.map((member) => (
                  <button
                    key={member.id}
                    onClick={() => handleAssignMember(member.id)}
                    className="w-full flex items-center justify-between p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <UserIcon className={`h-5 w-5 ${getMemberAvailabilityColor(member)}`} />
                      </div>
                      <div className="text-left">
                        <p className="font-medium text-gray-900">{member.name}</p>
                        <p className="text-sm text-gray-500">{member.role}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-500">{member.assignmentCount} asignaciones</p>
                      {!member.availability && (
                        <p className="text-xs text-red-500">No disponible</p>
                      )}
                    </div>
                  </button>
                ))}
                
                {filteredMembers.length === 0 && (
                  <div className="text-center py-8">
                    <UserIcon className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500">No se encontraron miembros</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
