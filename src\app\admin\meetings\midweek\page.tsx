'use client';

/**
 * Midweek Meeting Administration Page
 * 
 * Administrative interface for managing midweek meetings with enhanced JW.org integration,
 * assignment capabilities, and mobile-optimized design based on the old app screenshots.
 */

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  CalendarIcon,
  UserGroupIcon,
  Cog6ToothIcon,
  EyeIcon,
  PlusIcon,
  ArrowLeftIcon,
  ClockIcon,
  MapPinIcon,
  DocumentTextIcon,
  UsersIcon
} from '@heroicons/react/24/outline';

interface User {
  id: string;
  name: string;
  role: string;
  congregationId: string;
  congregationName: string;
  hasCongregationPinAccess?: boolean;
}

interface MidweekMeeting {
  id: string;
  meetingDate: string;
  theme?: string;
  chairman?: string;
  location: string;
  status: string;
  parts: MeetingPart[];
}

interface MeetingPart {
  id: string;
  partType: string;
  title: string;
  assignedMember?: string;
  assistant?: string;
  timeAllocation?: number;
  displayOrder: number;
}

interface TabConfig {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  color: string;
}

const tabs: TabConfig[] = [
  { id: 'schedule', label: 'Horario', icon: CalendarIcon, color: 'purple' },
  { id: 'assignments', label: 'Asignaciones', icon: UserGroupIcon, color: 'blue' },
  { id: 'members', label: 'Miembros', icon: UsersIcon, color: 'green' },
  { id: 'settings', label: 'Configuración', icon: Cog6ToothIcon, color: 'gray' },
];

export default function MidweekMeetingAdminPage() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [activeTab, setActiveTab] = useState('schedule');
  const [meetings, setMeetings] = useState<MidweekMeeting[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedDate, setSelectedDate] = useState<string>('');

  useEffect(() => {
    checkAuth();
    loadMeetings();
  }, []);

  const checkAuth = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        router.push('/auth/login');
        return;
      }

      const response = await fetch('/api/auth/verify', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        router.push('/auth/login');
        return;
      }

      const userData = await response.json();
      setUser(userData.user);

      // Check if user has admin permissions
      if (!['developer', 'overseer', 'elder'].includes(userData.user.role)) {
        router.push('/dashboard');
        return;
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      router.push('/auth/login');
    }
  };

  const loadMeetings = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      
      const response = await fetch('/api/meetings/midweek', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setMeetings(data.meetings || []);
      }
    } catch (error) {
      console.error('Failed to load meetings:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  const handleBackClick = () => {
    router.push('/admin');
  };

  const getTabColor = (tabId: string, isActive: boolean) => {
    const tab = tabs.find(t => t.id === tabId);
    if (!tab) return 'gray';
    
    if (isActive) {
      switch (tab.color) {
        case 'purple': return 'bg-purple-500 text-white';
        case 'blue': return 'bg-blue-500 text-white';
        case 'green': return 'bg-green-500 text-white';
        case 'gray': return 'bg-gray-500 text-white';
        default: return 'bg-purple-500 text-white';
      }
    } else {
      return 'bg-gray-100 text-gray-600 hover:bg-gray-200';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Cargando reuniones...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header with purple background matching the dashboard icon */}
      <div className="bg-purple-500 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBackClick}
                className="p-2 rounded-lg hover:bg-purple-600 transition-colors"
              >
                <ArrowLeftIcon className="h-6 w-6" />
              </button>
              <div>
                <h1 className="text-xl font-semibold">Administración de Reuniones Entre Semana</h1>
                <p className="text-purple-100 text-sm">{user?.congregationName}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-1 py-4">
            {tabs.map((tab) => {
              const IconComponent = tab.icon;
              const isActive = activeTab === tab.id;
              
              return (
                <button
                  key={tab.id}
                  onClick={() => handleTabChange(tab.id)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors ${getTabColor(tab.id, isActive)}`}
                >
                  <IconComponent className="h-5 w-5" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Content Area */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'schedule' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900">Horario de Reuniones</h2>
              <button className="bg-purple-500 text-white px-4 py-2 rounded-lg hover:bg-purple-600 transition-colors flex items-center space-x-2">
                <PlusIcon className="h-5 w-5" />
                <span>Nueva Reunión</span>
              </button>
            </div>

            {/* Meeting List */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Próximas Reuniones</h3>
                
                {meetings.length === 0 ? (
                  <div className="text-center py-8">
                    <CalendarIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">No hay reuniones programadas</p>
                    <p className="text-sm text-gray-400 mt-2">Haz clic en "Nueva Reunión" para comenzar</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {meetings.map((meeting) => (
                      <div key={meeting.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-4">
                              <div className="flex-shrink-0">
                                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                  <CalendarIcon className="h-6 w-6 text-purple-600" />
                                </div>
                              </div>
                              <div className="flex-1 min-w-0">
                                <h4 className="text-lg font-medium text-gray-900 truncate">
                                  {meeting.theme || 'Vida y Ministerio Cristianos'}
                                </h4>
                                <div className="flex items-center space-x-4 mt-1 text-sm text-gray-500">
                                  <div className="flex items-center space-x-1">
                                    <ClockIcon className="h-4 w-4" />
                                    <span>{new Date(meeting.meetingDate).toLocaleDateString('es-ES')}</span>
                                  </div>
                                  <div className="flex items-center space-x-1">
                                    <MapPinIcon className="h-4 w-4" />
                                    <span>{meeting.location}</span>
                                  </div>
                                  {meeting.chairman && (
                                    <div className="flex items-center space-x-1">
                                      <UserGroupIcon className="h-4 w-4" />
                                      <span>Presidente: {meeting.chairman}</span>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                              <EyeIcon className="h-5 w-5" />
                            </button>
                            <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                              <DocumentTextIcon className="h-5 w-5" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'assignments' && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">Asignaciones</h2>
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <p className="text-gray-500">Gestión de asignaciones - En desarrollo</p>
            </div>
          </div>
        )}

        {activeTab === 'members' && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">Miembros</h2>
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <p className="text-gray-500">Gestión de miembros - En desarrollo</p>
            </div>
          </div>
        )}

        {activeTab === 'settings' && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">Configuración</h2>
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <p className="text-gray-500">Configuración de reuniones - En desarrollo</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
