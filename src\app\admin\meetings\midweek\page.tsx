'use client';

/**
 * Complete Midweek Meeting Administration Interface
 *
 * Comprehensive administrative interface for managing midweek meetings exactly
 * matching the reference screenshots with calendar, past meetings, member
 * configuration, and meeting settings.
 */

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  CalendarIcon,
  UserGroupIcon,
  Cog6ToothIcon,
  EyeIcon,
  PlusIcon,
  ArrowLeftIcon,
  ClockIcon,
  MapPinIcon,
  DocumentTextIcon,
  UsersIcon,
  PencilIcon,
  TrashIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline';
import MidweekImportModal from '@/components/meetings/MidweekImportModal';

interface User {
  id: string;
  name: string;
  role: string;
  congregationId: string;
  congregationName: string;
  hasCongregationPinAccess?: boolean;
}

interface MidweekMeeting {
  id: string;
  meetingDate: string;
  meetingTime: string;
  theme?: string;
  chairman?: string;
  location: string;
  status: string;
  parts: MeetingPart[];
}

interface MeetingPart {
  id: string;
  partType: string;
  title: string;
  assignedMember?: string;
  assistant?: string;
  timeAllocation?: number;
  displayOrder: number;
}

interface MemberAssignment {
  partType: string;
  partTitle: string;
  members: Member[];
  qualificationRequired?: string[];
}

interface Member {
  id: string;
  name: string;
  role: string;
  qualifications: string[];
}

interface MeetingConfiguration {
  dayOfWeek: string;
  time: string;
  location: string;
  zoomMeetingId?: string;
  zoomPassword?: string;
}

interface TabConfig {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  color: string;
}

const tabs: TabConfig[] = [
  { id: 'calendar', label: 'Calendario de Reuniones', icon: CalendarIcon, color: 'purple' },
  { id: 'past', label: 'Reuniones Pasadas', icon: DocumentTextIcon, color: 'purple' },
  { id: 'members', label: 'Miembros', icon: UsersIcon, color: 'purple' },
  { id: 'settings', label: '', icon: Cog6ToothIcon, color: 'purple' }, // Settings icon only
];

// Sample meeting data matching the screenshots
const sampleMeetings: MidweekMeeting[] = [
  {
    id: '1',
    meetingDate: '2025-07-25',
    meetingTime: '19:30',
    theme: 'Vida y Ministerio Cristianos',
    chairman: undefined,
    location: 'Salón del Reino',
    status: 'scheduled',
    parts: []
  },
  {
    id: '2',
    meetingDate: '2025-08-15',
    meetingTime: '19:30',
    theme: 'Vida y Ministerio Cristianos',
    chairman: undefined,
    location: 'Salón del Reino',
    status: 'scheduled',
    parts: []
  },
  {
    id: '3',
    meetingDate: '2025-08-22',
    meetingTime: '19:30',
    theme: 'Vida y Ministerio Cristianos',
    chairman: undefined,
    location: 'Salón del Reino',
    status: 'scheduled',
    parts: []
  }
];

// Sample past meetings
const samplePastMeetings: MidweekMeeting[] = [
  {
    id: 'past1',
    meetingDate: '2025-07-18',
    meetingTime: '19:30',
    theme: 'Vida y Ministerio Cristianos',
    chairman: undefined,
    location: 'Salón del Reino',
    status: 'completed',
    parts: []
  },
  {
    id: 'past2',
    meetingDate: '2025-07-04',
    meetingTime: '19:30',
    theme: 'Vida y Ministerio Cristianos',
    chairman: 'Horacio Cerda',
    location: 'Salón del Reino',
    status: 'completed',
    parts: []
  },
  {
    id: 'past3',
    meetingDate: '2025-06-27',
    meetingTime: '19:30',
    theme: 'Vida y Ministerio Cristianos',
    chairman: undefined,
    location: 'Salón del Reino',
    status: 'completed',
    parts: []
  },
  {
    id: 'past4',
    meetingDate: '2025-06-20',
    meetingTime: '19:30',
    theme: 'Vida y Ministerio Cristianos',
    chairman: undefined,
    location: 'Salón del Reino',
    status: 'completed',
    parts: []
  },
  {
    id: 'past5',
    meetingDate: '2025-06-13',
    meetingTime: '19:30',
    theme: 'Vida y Ministerio Cristianos',
    chairman: 'Pastor Rodriguez',
    location: 'Salón del Reino',
    status: 'completed',
    parts: []
  },
  {
    id: 'past6',
    meetingDate: '2025-06-06',
    meetingTime: '19:30',
    theme: 'Vida y Ministerio Cristianos',
    chairman: 'Horacio Cerda',
    location: 'Salón del Reino',
    status: 'completed',
    parts: []
  },
  {
    id: 'past7',
    meetingDate: '2025-05-30',
    meetingTime: '19:30',
    theme: 'Vida y Ministerio Cristianos',
    chairman: undefined,
    location: 'Salón del Reino',
    status: 'completed',
    parts: []
  }
];

export default function MidweekMeetingAdminPage() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [activeTab, setActiveTab] = useState('calendar');
  const [meetings, setMeetings] = useState<MidweekMeeting[]>(sampleMeetings);
  const [pastMeetings, setPastMeetings] = useState<MidweekMeeting[]>(samplePastMeetings);
  const [loading, setLoading] = useState(false); // Set to false for demo
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [meetingConfig, setMeetingConfig] = useState<MeetingConfiguration>({
    dayOfWeek: 'Viernes',
    time: '07:30 PM',
    location: 'Salon del Reino',
    zoomMeetingId: '',
    zoomPassword: ''
  });
  const [configTab, setConfigTab] = useState('meetings'); // For settings sub-tabs
  const [showImportModal, setShowImportModal] = useState(false);

  useEffect(() => {
    // For demo purposes, set a mock user
    setUser({
      id: 'demo-user',
      name: 'Demo Admin',
      role: 'elder',
      congregationId: '1441',
      congregationName: 'Coral Oeste'
    });
  }, []);

  // Helper function to format dates in Spanish
  const formatSpanishDate = (dateString: string): string => {
    const date = new Date(dateString);
    const days = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'];
    const months = [
      'enero', 'febrero', 'marzo', 'abril', 'mayo', 'junio',
      'julio', 'agosto', 'septiembre', 'octubre', 'noviembre', 'diciembre'
    ];

    const dayName = days[date.getDay()];
    const day = date.getDate();
    const month = months[date.getMonth()];
    const year = date.getFullYear();

    return `${dayName}, ${day} de ${month} de ${year}`;
  };

  // Handle meeting actions
  const handleViewMeeting = (meetingId: string) => {
    console.log('View meeting:', meetingId);
    // Implementation for viewing meeting details
  };

  const handleEditMeeting = (meetingId: string) => {
    console.log('Edit meeting:', meetingId);
    // Implementation for editing meeting
  };

  const handleDeleteMeeting = (meetingId: string) => {
    console.log('Delete meeting:', meetingId);
    // Implementation for deleting meeting
  };

  const handleAddMeeting = () => {
    console.log('Add new meeting');
    // Implementation for adding new meeting
  };

  const handleImportFromJW = () => {
    setShowImportModal(true);
  };

  const handleImportComplete = () => {
    // Refresh the meetings data after import
    // In a real implementation, this would fetch from the API
    console.log('Import completed, refreshing meetings...');
    setShowImportModal(false);
  };

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  const handleBackClick = () => {
    router.push('/admin');
  };

  const getTabColor = (tabId: string, isActive: boolean) => {
    if (isActive) {
      return 'bg-purple-500 text-white border-b-2 border-purple-500';
    } else {
      return 'bg-white text-gray-600 hover:bg-gray-50 border-b-2 border-transparent';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header with purple background matching the screenshots */}
      <div className="bg-purple-500 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBackClick}
                className="p-2 rounded-lg hover:bg-purple-600 transition-colors"
              >
                <ArrowLeftIcon className="h-6 w-6" />
              </button>
              <div>
                <h1 className="text-xl font-semibold">Administración de Reuniones Entre Semana</h1>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Container */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* White Container matching screenshots */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {/* Title Section */}
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">Administración de Reuniones Entre Semana</h2>
          </div>

          {/* Tab Navigation */}
          <div className="border-b border-gray-200">
            <div className="flex space-x-0">
              {tabs.map((tab) => {
                const IconComponent = tab.icon;
                const isActive = activeTab === tab.id;

                return (
                  <button
                    key={tab.id}
                    onClick={() => handleTabChange(tab.id)}
                    className={`px-6 py-3 text-sm font-medium transition-colors ${getTabColor(tab.id, isActive)}`}
                  >
                    {tab.id === 'settings' ? (
                      <IconComponent className="h-5 w-5" />
                    ) : (
                      tab.label
                    )}
                  </button>
                );
              })}
            </div>
          </div>

          {/* Content Area */}
          <div className="p-6">
            {/* Calendar Tab */}
            {activeTab === 'calendar' && (
              <div>
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-semibold text-gray-900">Calendario de Reuniones</h3>
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={handleImportFromJW}
                      className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors flex items-center space-x-2"
                    >
                      <ArrowDownTrayIcon className="h-5 w-5" />
                      <span>Importar desde JW.org</span>
                    </button>
                    <button
                      onClick={handleAddMeeting}
                      className="bg-purple-500 text-white px-4 py-2 rounded-lg hover:bg-purple-600 transition-colors flex items-center space-x-2"
                    >
                      <PlusIcon className="h-5 w-5" />
                      <span>Agregar Reunión</span>
                    </button>
                  </div>
                </div>

                {/* Meeting Table */}
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Fecha</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Hora</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Lugar</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Presidente</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Acciones</th>
                      </tr>
                    </thead>
                    <tbody>
                      {meetings.map((meeting) => (
                        <tr key={meeting.id} className="border-b border-gray-100 hover:bg-gray-50">
                          <td className="py-3 px-4 text-gray-900">
                            {formatSpanishDate(meeting.meetingDate)}
                          </td>
                          <td className="py-3 px-4 text-gray-900">{meeting.meetingTime}</td>
                          <td className="py-3 px-4 text-gray-900">{meeting.location}</td>
                          <td className="py-3 px-4 text-gray-900">
                            {meeting.chairman || 'No asignado'}
                          </td>
                          <td className="py-3 px-4">
                            <div className="flex items-center space-x-2">
                              <button
                                onClick={() => handleViewMeeting(meeting.id)}
                                className="bg-purple-500 text-white px-3 py-1 rounded text-sm hover:bg-purple-600 transition-colors flex items-center space-x-1"
                              >
                                <EyeIcon className="h-4 w-4" />
                                <span>Ver</span>
                              </button>
                              <button
                                onClick={() => handleEditMeeting(meeting.id)}
                                className="bg-gray-100 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-200 transition-colors flex items-center space-x-1"
                              >
                                <PencilIcon className="h-4 w-4" />
                                <span>Editar</span>
                              </button>
                              <button
                                onClick={() => handleDeleteMeeting(meeting.id)}
                                className="bg-gray-100 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-200 transition-colors flex items-center space-x-1"
                              >
                                <TrashIcon className="h-4 w-4" />
                                <span>Eliminar</span>
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* Past Meetings Tab */}
            {activeTab === 'past' && (
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-6">Reuniones Pasadas</h3>

                {/* Past Meetings Table */}
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Fecha</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Hora</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Lugar</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Presidente</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Acciones</th>
                      </tr>
                    </thead>
                    <tbody>
                      {pastMeetings.map((meeting) => (
                        <tr key={meeting.id} className="border-b border-gray-100 hover:bg-gray-50">
                          <td className="py-3 px-4 text-gray-900">
                            {formatSpanishDate(meeting.meetingDate)}
                          </td>
                          <td className="py-3 px-4 text-gray-900">{meeting.meetingTime}</td>
                          <td className="py-3 px-4 text-gray-900">{meeting.location}</td>
                          <td className="py-3 px-4 text-gray-900">
                            {meeting.chairman || 'No asignado'}
                          </td>
                          <td className="py-3 px-4">
                            <div className="flex items-center space-x-2">
                              <button
                                onClick={() => handleViewMeeting(meeting.id)}
                                className="bg-purple-500 text-white px-3 py-1 rounded text-sm hover:bg-purple-600 transition-colors flex items-center space-x-1"
                              >
                                <EyeIcon className="h-4 w-4" />
                                <span>Ver</span>
                              </button>
                              <button
                                onClick={() => handleEditMeeting(meeting.id)}
                                className="bg-gray-100 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-200 transition-colors flex items-center space-x-1"
                              >
                                <PencilIcon className="h-4 w-4" />
                                <span>Editar</span>
                              </button>
                              <button
                                onClick={() => handleDeleteMeeting(meeting.id)}
                                className="bg-gray-100 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-200 transition-colors flex items-center space-x-1"
                              >
                                <TrashIcon className="h-4 w-4" />
                                <span>Eliminar</span>
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
            {/* Members Tab */}
            {activeTab === 'members' && (
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-6">Configuración de Miembros</h3>

                {/* Member Assignment Sections - Matching Screenshot Layout */}
                <div className="space-y-6">
                  {/* Configuración de Miembros Header */}
                  <div className="bg-purple-500 text-white px-4 py-2 rounded-t-lg">
                    <h4 className="font-medium">Configuración de Miembros</h4>
                  </div>

                  <div className="border border-gray-200 rounded-b-lg p-6 space-y-8">
                    {/* Miembros para Oración */}
                    <div>
                      <h5 className="font-medium text-gray-900 mb-3">Miembros para Oración</h5>
                      <div className="border border-gray-300 rounded p-4 min-h-[100px] bg-gray-50 relative">
                        <div className="space-y-1 text-sm text-gray-700">
                          <div>Richard Rubi</div>
                          <div>Horacio Cerda</div>
                          <div>Yoan Developer</div>
                          <div>James Rubi</div>
                          <div>David Fernandez</div>
                        </div>
                        <div className="absolute top-2 right-2">
                          <button className="text-gray-400 hover:text-gray-600">
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                              <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                            </svg>
                          </button>
                        </div>
                      </div>
                      <p className="text-xs text-gray-500 mt-2">
                        Selecciona los miembros que pueden hacer oraciones (Elders, Ministerial Servants)
                      </p>
                    </div>

                    {/* Miembros para Tesoros de la Biblia */}
                    <div>
                      <h5 className="font-medium text-gray-900 mb-3">Miembros para Tesoros de la Biblia</h5>
                      <div className="border border-gray-300 rounded p-4 min-h-[100px] bg-gray-50 relative">
                        <div className="space-y-1 text-sm text-gray-700">
                          <div>Richard Rubi</div>
                          <div>Horacio Cerda</div>
                          <div>Yoan Developer</div>
                          <div>James Rubi</div>
                          <div>David Fernandez</div>
                        </div>
                        <div className="absolute top-2 right-2">
                          <button className="text-gray-400 hover:text-gray-600">
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                              <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                            </svg>
                          </button>
                        </div>
                      </div>
                      <p className="text-xs text-gray-500 mt-2">
                        Selecciona los miembros que pueden presentar partes de Tesoros de la Biblia (Elders, Ministerial Servants)
                      </p>
                    </div>

                    {/* Miembros para Busquemos Perlas Escondidas */}
                    <div>
                      <h5 className="font-medium text-gray-900 mb-3">Miembros para Busquemos Perlas Escondidas</h5>
                      <div className="border border-gray-300 rounded p-4 min-h-[100px] bg-gray-50 relative">
                        <div className="space-y-1 text-sm text-gray-700">
                          <div>Yoan Valiente</div>
                          <div>Raul Ramirez</div>
                          <div>Carlos De La Torre</div>
                          <div>Manuel Jimenez</div>
                          <div>Marlon Rodriguez</div>
                          <div>Enrique Nuñez</div>
                        </div>
                        <div className="absolute top-2 right-2">
                          <button className="text-gray-400 hover:text-gray-600">
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                              <path d="M10 6a2 2 0 110-4 2 2 0 110 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                            </svg>
                          </button>
                        </div>
                      </div>
                      <p className="text-xs text-gray-500 mt-2">
                        Selecciona los miembros que pueden presentar la parte de Busquemos Perlas Escondidas (Ministerial Servants, Elders)
                      </p>
                    </div>

                    {/* Miembros para Lectura de la Biblia */}
                    <div>
                      <h5 className="font-medium text-gray-900 mb-3">Miembros para Lectura de la Biblia</h5>
                      <div className="border border-gray-300 rounded p-4 min-h-[100px] bg-gray-50 relative">
                        <div className="space-y-1 text-sm text-gray-700">
                          <div>Yalexis Marquetis</div>
                          <div>Hermano Marquetis</div>
                          <div>Juana Gutierrez</div>
                          <div>Josefa Cejas</div>
                          <div>Belkis Perez</div>
                          <div>Yanira Gonzalez</div>
                          <div>Juan Carlos Gonzalez</div>
                        </div>
                        <div className="absolute top-2 right-2">
                          <button className="text-gray-400 hover:text-gray-600">
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                              <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                            </svg>
                          </button>
                        </div>
                      </div>
                      <p className="text-xs text-gray-500 mt-2">
                        Selecciona los miembros que pueden hacer la lectura de la Biblia (Ministerial Servants, Publishers)
                      </p>
                    </div>

                    {/* Miembros para Seamos Mejores Maestros */}
                    <div>
                      <h5 className="font-medium text-gray-900 mb-3">Miembros para Seamos Mejores Maestros</h5>
                      <div className="border border-gray-300 rounded p-4 min-h-[100px] bg-gray-50 relative">
                        <div className="space-y-1 text-sm text-gray-700">
                          <div>Yoan Valiente</div>
                          <div>Raul Ramirez</div>
                          <div>Carlos De La Torre</div>
                          <div>Manuel Jimenez</div>
                          <div>Marlon Rodriguez</div>
                          <div>Enrique Nuñez</div>
                          <div>Lourdes Rubi</div>
                        </div>
                        <div className="absolute top-2 right-2">
                          <button className="text-gray-400 hover:text-gray-600">
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                              <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                            </svg>
                          </button>
                        </div>
                      </div>
                      <p className="text-xs text-gray-500 mt-2">
                        Selecciona los miembros que pueden presentar partes de Seamos Mejores Maestros (Ministerial Servants, Publishers)
                      </p>
                    </div>

                    {/* Miembros para Discurso */}
                    <div>
                      <h5 className="font-medium text-gray-900 mb-3">Miembros para Discurso</h5>
                      <div className="border border-gray-300 rounded p-4 min-h-[100px] bg-gray-50 relative">
                        <div className="space-y-1 text-sm text-gray-700">
                          <div>Yoan Valiente</div>
                          <div>Raul Ramirez</div>
                          <div>Carlos De La Torre</div>
                          <div>Manuel Jimenez</div>
                          <div>Marlon Rodriguez</div>
                          <div>Enrique Nuñez</div>
                          <div>Lourdes Rubi</div>
                        </div>
                        <div className="absolute top-2 right-2">
                          <button className="text-gray-400 hover:text-gray-600">
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                              <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                            </svg>
                          </button>
                        </div>
                      </div>
                      <p className="text-xs text-gray-500 mt-2">
                        Selecciona los miembros que pueden presentar el Discurso en Seamos Mejores Maestros (Ministerial Servants, Publishers)
                      </p>
                    </div>

                    {/* Miembros para Nuestra Vida Cristiana */}
                    <div>
                      <h5 className="font-medium text-gray-900 mb-3">Miembros para Nuestra Vida Cristiana</h5>
                      <div className="border border-gray-300 rounded p-4 min-h-[100px] bg-gray-50 relative">
                        <div className="space-y-1 text-sm text-gray-700">
                          <div>Richard Rubi</div>
                          <div>Horacio Cerda</div>
                          <div>Yoan Developer</div>
                          <div>James Rubi</div>
                          <div>David Fernandez</div>
                          <div>Jorge Diaz</div>
                          <div>Javier Torres</div>
                        </div>
                        <div className="absolute top-2 right-2">
                          <button className="text-gray-400 hover:text-gray-600">
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                              <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                            </svg>
                          </button>
                        </div>
                      </div>
                      <p className="text-xs text-gray-500 mt-2">
                        Selecciona los miembros que pueden presentar partes de Nuestra Vida Cristiana (Elders, Ministerial Servants)
                      </p>
                    </div>

                    {/* Miembros para Estudio Bíblico de Congregación */}
                    <div>
                      <h5 className="font-medium text-gray-900 mb-3">Miembros para Estudio Bíblico de Congregación</h5>
                      <div className="border border-gray-300 rounded p-4 min-h-[100px] bg-gray-50 relative">
                        <div className="space-y-1 text-sm text-gray-700">
                          <div>Richard Rubi</div>
                          <div>Horacio Cerda</div>
                          <div>Yoan Developer</div>
                          <div>James Rubi</div>
                          <div>David Fernandez</div>
                          <div>Jorge Diaz</div>
                          <div>Javier Torres</div>
                        </div>
                        <div className="absolute top-2 right-2">
                          <button className="text-gray-400 hover:text-gray-600">
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                              <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                            </svg>
                          </button>
                        </div>
                      </div>
                      <p className="text-xs text-gray-500 mt-2">
                        Selecciona los miembros que pueden conducir el Estudio Bíblico de Congregación (Elders)
                      </p>
                    </div>
                  </div>

                  {/* Save Button */}
                  <div className="flex justify-end">
                    <button className="bg-purple-500 text-white px-6 py-2 rounded-lg hover:bg-purple-600 transition-colors">
                      Guardar Configuración de Miembros
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Settings Tab */}
            {activeTab === 'settings' && (
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-6">Configuración de Reuniones</h3>

                {/* Settings Sub-tabs */}
                <div className="border-b border-gray-200 mb-6">
                  <div className="flex space-x-0">
                    <button
                      onClick={() => setConfigTab('meetings')}
                      className={`px-4 py-2 text-sm font-medium transition-colors ${
                        configTab === 'meetings'
                          ? 'bg-purple-500 text-white border-b-2 border-purple-500'
                          : 'bg-white text-gray-600 hover:bg-gray-50 border-b-2 border-transparent'
                      }`}
                    >
                      Configuración de Reuniones
                    </button>
                    <button
                      onClick={() => setConfigTab('members')}
                      className={`px-4 py-2 text-sm font-medium transition-colors ${
                        configTab === 'members'
                          ? 'bg-purple-500 text-white border-b-2 border-purple-500'
                          : 'bg-white text-gray-600 hover:bg-gray-50 border-b-2 border-transparent'
                      }`}
                    >
                      Miembros
                    </button>
                  </div>
                </div>

                {/* Configuration Content */}
                {configTab === 'meetings' && (
                  <div className="space-y-6">
                    {/* Configuración General */}
                    <div className="bg-purple-500 text-white px-4 py-2 rounded-t-lg">
                      <h4 className="font-medium">Configuración General</h4>
                    </div>

                    <div className="border border-gray-200 rounded-b-lg p-6 space-y-6">
                      {/* Día de la semana predeterminado */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Día de la semana predeterminado
                        </label>
                        <select
                          value={meetingConfig.dayOfWeek}
                          onChange={(e) => setMeetingConfig({...meetingConfig, dayOfWeek: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        >
                          <option value="Lunes">Lunes</option>
                          <option value="Martes">Martes</option>
                          <option value="Miércoles">Miércoles</option>
                          <option value="Jueves">Jueves</option>
                          <option value="Viernes">Viernes</option>
                          <option value="Sábado">Sábado</option>
                          <option value="Domingo">Domingo</option>
                        </select>
                      </div>

                      {/* Hora predeterminada */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Hora predeterminada
                        </label>
                        <input
                          type="time"
                          value="19:30"
                          onChange={(e) => setMeetingConfig({...meetingConfig, time: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        />
                      </div>

                      {/* Lugar predeterminado */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Lugar predeterminado
                        </label>
                        <input
                          type="text"
                          value={meetingConfig.location}
                          onChange={(e) => setMeetingConfig({...meetingConfig, location: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                          placeholder="Salon del Reino"
                        />
                      </div>
                    </div>

                    {/* Configuración de Zoom */}
                    <div className="bg-purple-500 text-white px-4 py-2 rounded-t-lg">
                      <h4 className="font-medium">Configuración de Zoom</h4>
                    </div>

                    <div className="border border-gray-200 rounded-b-lg p-6 space-y-6">
                      {/* ID de reunión de Zoom predeterminado */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          ID de reunión de Zoom predeterminado
                        </label>
                        <input
                          type="text"
                          value={meetingConfig.zoomMeetingId || ''}
                          onChange={(e) => setMeetingConfig({...meetingConfig, zoomMeetingId: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                          placeholder="Ingrese el ID de la reunión de Zoom"
                        />
                      </div>

                      {/* Contraseña de Zoom */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Contraseña de Zoom (opcional)
                        </label>
                        <input
                          type="text"
                          value={meetingConfig.zoomPassword || ''}
                          onChange={(e) => setMeetingConfig({...meetingConfig, zoomPassword: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                          placeholder="Contraseña de la reunión"
                        />
                      </div>
                    </div>

                    {/* Save Button */}
                    <div className="flex justify-end">
                      <button className="bg-purple-500 text-white px-6 py-2 rounded-lg hover:bg-purple-600 transition-colors">
                        Guardar Configuración
                      </button>
                    </div>
                  </div>
                )}

                {configTab === 'members' && (
                  <div className="text-center py-8">
                    <p className="text-gray-500">Configuración de miembros disponible en la pestaña "Miembros"</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Import Modal */}
      <MidweekImportModal
        isOpen={showImportModal}
        onClose={() => setShowImportModal(false)}
        onImportComplete={handleImportComplete}
      />
    </div>
  );
}
