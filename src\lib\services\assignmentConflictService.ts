/**
 * Assignment Conflict Detection Service
 * 
 * Detects and resolves scheduling conflicts for meeting assignments
 * with advanced conflict detection algorithms and resolution suggestions.
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export interface AssignmentConflict {
  id: string;
  type: 'scheduling' | 'qualification' | 'availability' | 'frequency' | 'workload';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  affectedAssignments: ConflictAssignment[];
  suggestedResolutions: ResolutionSuggestion[];
  autoResolvable: boolean;
}

export interface ConflictAssignment {
  meetingId: string;
  meetingDate: Date;
  partId: string;
  partTitle: string;
  memberId: string;
  memberName: string;
  assistantId?: string;
  assistantName?: string;
}

export interface ResolutionSuggestion {
  type: 'reassign' | 'swap' | 'reschedule' | 'split';
  description: string;
  confidence: number; // 0-100
  alternativeMembers?: AlternativeMember[];
  swapOptions?: SwapOption[];
}

export interface AlternativeMember {
  memberId: string;
  memberName: string;
  qualificationMatch: number; // 0-100
  availabilityScore: number; // 0-100
  workloadScore: number; // 0-100
  overallScore: number; // 0-100
}

export interface SwapOption {
  memberA: string;
  memberB: string;
  partA: string;
  partB: string;
  feasibilityScore: number; // 0-100
}

export interface ConflictDetectionOptions {
  checkScheduling: boolean;
  checkQualifications: boolean;
  checkAvailability: boolean;
  checkFrequency: boolean;
  checkWorkload: boolean;
  timeWindow: number; // Days to check for conflicts
  maxAssignmentsPerMonth: number;
}

export interface MemberQualification {
  memberId: string;
  qualifications: string[];
  restrictions: string[];
  preferredParts: string[];
  availabilityPattern: AvailabilityPattern[];
}

export interface AvailabilityPattern {
  dayOfWeek: number; // 0-6 (Sunday-Saturday)
  available: boolean;
  startDate?: Date;
  endDate?: Date;
  reason?: string;
}

export class AssignmentConflictService {
  /**
   * Detect conflicts for proposed assignments
   */
  static async detectConflicts(
    congregationId: string,
    proposedAssignments: ConflictAssignment[],
    options: ConflictDetectionOptions = this.getDefaultOptions()
  ): Promise<AssignmentConflict[]> {
    const conflicts: AssignmentConflict[] = [];

    try {
      // Load existing assignments and member data
      const existingAssignments = await this.loadExistingAssignments(congregationId, options.timeWindow);
      const memberQualifications = await this.loadMemberQualifications(congregationId);
      const memberAvailability = await this.loadMemberAvailability(congregationId);

      // Check each type of conflict
      if (options.checkScheduling) {
        const schedulingConflicts = await this.detectSchedulingConflicts(
          proposedAssignments,
          existingAssignments
        );
        conflicts.push(...schedulingConflicts);
      }

      if (options.checkQualifications) {
        const qualificationConflicts = await this.detectQualificationConflicts(
          proposedAssignments,
          memberQualifications
        );
        conflicts.push(...qualificationConflicts);
      }

      if (options.checkAvailability) {
        const availabilityConflicts = await this.detectAvailabilityConflicts(
          proposedAssignments,
          memberAvailability
        );
        conflicts.push(...availabilityConflicts);
      }

      if (options.checkFrequency) {
        const frequencyConflicts = await this.detectFrequencyConflicts(
          proposedAssignments,
          existingAssignments,
          options.maxAssignmentsPerMonth
        );
        conflicts.push(...frequencyConflicts);
      }

      if (options.checkWorkload) {
        const workloadConflicts = await this.detectWorkloadConflicts(
          proposedAssignments,
          existingAssignments
        );
        conflicts.push(...workloadConflicts);
      }

      // Generate resolution suggestions for each conflict
      for (const conflict of conflicts) {
        conflict.suggestedResolutions = await this.generateResolutionSuggestions(
          conflict,
          memberQualifications,
          memberAvailability,
          existingAssignments
        );
      }

      return conflicts;
    } catch (error) {
      console.error('Error detecting conflicts:', error);
      throw new Error('Failed to detect assignment conflicts');
    }
  }

  /**
   * Detect scheduling conflicts (same member assigned to multiple parts on same date)
   */
  private static async detectSchedulingConflicts(
    proposedAssignments: ConflictAssignment[],
    existingAssignments: ConflictAssignment[]
  ): Promise<AssignmentConflict[]> {
    const conflicts: AssignmentConflict[] = [];
    const allAssignments = [...proposedAssignments, ...existingAssignments];

    // Group assignments by member and date
    const memberDateMap = new Map<string, Map<string, ConflictAssignment[]>>();

    for (const assignment of allAssignments) {
      const memberKey = assignment.memberId;
      const dateKey = assignment.meetingDate.toISOString().split('T')[0];

      if (!memberDateMap.has(memberKey)) {
        memberDateMap.set(memberKey, new Map());
      }

      const memberDates = memberDateMap.get(memberKey)!;
      if (!memberDates.has(dateKey)) {
        memberDates.set(dateKey, []);
      }

      memberDates.get(dateKey)!.push(assignment);
    }

    // Check for conflicts
    for (const [memberId, dateMap] of memberDateMap) {
      for (const [date, assignments] of dateMap) {
        if (assignments.length > 1) {
          conflicts.push({
            id: `scheduling-${memberId}-${date}`,
            type: 'scheduling',
            severity: 'high',
            description: `${assignments[0].memberName} está asignado a múltiples partes el ${date}`,
            affectedAssignments: assignments,
            suggestedResolutions: [],
            autoResolvable: true,
          });
        }
      }
    }

    return conflicts;
  }

  /**
   * Detect qualification conflicts (member not qualified for assigned part)
   */
  private static async detectQualificationConflicts(
    proposedAssignments: ConflictAssignment[],
    memberQualifications: MemberQualification[]
  ): Promise<AssignmentConflict[]> {
    const conflicts: AssignmentConflict[] = [];
    const qualificationMap = new Map(
      memberQualifications.map(mq => [mq.memberId, mq])
    );

    for (const assignment of proposedAssignments) {
      const memberQual = qualificationMap.get(assignment.memberId);
      if (!memberQual) continue;

      const partRequirements = this.getPartRequirements(assignment.partTitle);
      const hasRequiredQualifications = partRequirements.every(req =>
        memberQual.qualifications.includes(req)
      );

      const hasRestrictions = partRequirements.some(req =>
        memberQual.restrictions.includes(req)
      );

      if (!hasRequiredQualifications || hasRestrictions) {
        conflicts.push({
          id: `qualification-${assignment.memberId}-${assignment.partId}`,
          type: 'qualification',
          severity: hasRestrictions ? 'critical' : 'high',
          description: `${assignment.memberName} no está calificado para "${assignment.partTitle}"`,
          affectedAssignments: [assignment],
          suggestedResolutions: [],
          autoResolvable: true,
        });
      }
    }

    return conflicts;
  }

  /**
   * Detect availability conflicts (member not available on assigned date)
   */
  private static async detectAvailabilityConflicts(
    proposedAssignments: ConflictAssignment[],
    memberAvailability: Map<string, AvailabilityPattern[]>
  ): Promise<AssignmentConflict[]> {
    const conflicts: AssignmentConflict[] = [];

    for (const assignment of proposedAssignments) {
      const availability = memberAvailability.get(assignment.memberId);
      if (!availability) continue;

      const dayOfWeek = assignment.meetingDate.getDay();
      const isAvailable = availability.some(pattern => {
        if (pattern.dayOfWeek !== dayOfWeek) return false;
        if (!pattern.available) return false;
        
        if (pattern.startDate && assignment.meetingDate < pattern.startDate) return false;
        if (pattern.endDate && assignment.meetingDate > pattern.endDate) return false;
        
        return true;
      });

      if (!isAvailable) {
        conflicts.push({
          id: `availability-${assignment.memberId}-${assignment.partId}`,
          type: 'availability',
          severity: 'medium',
          description: `${assignment.memberName} no está disponible el ${assignment.meetingDate.toLocaleDateString()}`,
          affectedAssignments: [assignment],
          suggestedResolutions: [],
          autoResolvable: true,
        });
      }
    }

    return conflicts;
  }

  /**
   * Detect frequency conflicts (member assigned too frequently)
   */
  private static async detectFrequencyConflicts(
    proposedAssignments: ConflictAssignment[],
    existingAssignments: ConflictAssignment[],
    maxAssignmentsPerMonth: number
  ): Promise<AssignmentConflict[]> {
    const conflicts: AssignmentConflict[] = [];
    const allAssignments = [...proposedAssignments, ...existingAssignments];

    // Group assignments by member and month
    const memberMonthMap = new Map<string, Map<string, ConflictAssignment[]>>();

    for (const assignment of allAssignments) {
      const memberKey = assignment.memberId;
      const monthKey = `${assignment.meetingDate.getFullYear()}-${assignment.meetingDate.getMonth()}`;

      if (!memberMonthMap.has(memberKey)) {
        memberMonthMap.set(memberKey, new Map());
      }

      const memberMonths = memberMonthMap.get(memberKey)!;
      if (!memberMonths.has(monthKey)) {
        memberMonths.set(monthKey, []);
      }

      memberMonths.get(monthKey)!.push(assignment);
    }

    // Check for frequency violations
    for (const [memberId, monthMap] of memberMonthMap) {
      for (const [month, assignments] of monthMap) {
        if (assignments.length > maxAssignmentsPerMonth) {
          conflicts.push({
            id: `frequency-${memberId}-${month}`,
            type: 'frequency',
            severity: 'medium',
            description: `${assignments[0].memberName} tiene demasiadas asignaciones en ${month} (${assignments.length}/${maxAssignmentsPerMonth})`,
            affectedAssignments: assignments,
            suggestedResolutions: [],
            autoResolvable: false,
          });
        }
      }
    }

    return conflicts;
  }

  /**
   * Detect workload conflicts (uneven distribution of assignments)
   */
  private static async detectWorkloadConflicts(
    proposedAssignments: ConflictAssignment[],
    existingAssignments: ConflictAssignment[]
  ): Promise<AssignmentConflict[]> {
    const conflicts: AssignmentConflict[] = [];
    const allAssignments = [...proposedAssignments, ...existingAssignments];

    // Calculate assignment counts per member
    const memberCounts = new Map<string, number>();
    for (const assignment of allAssignments) {
      const current = memberCounts.get(assignment.memberId) || 0;
      memberCounts.set(assignment.memberId, current + 1);
    }

    // Calculate average and identify outliers
    const counts = Array.from(memberCounts.values());
    const average = counts.reduce((sum, count) => sum + count, 0) / counts.length;
    const threshold = average * 1.5; // 50% above average

    for (const [memberId, count] of memberCounts) {
      if (count > threshold) {
        const memberAssignments = allAssignments.filter(a => a.memberId === memberId);
        conflicts.push({
          id: `workload-${memberId}`,
          type: 'workload',
          severity: 'low',
          description: `${memberAssignments[0].memberName} tiene una carga de trabajo alta (${count} vs promedio ${Math.round(average)})`,
          affectedAssignments: memberAssignments,
          suggestedResolutions: [],
          autoResolvable: false,
        });
      }
    }

    return conflicts;
  }

  /**
   * Generate resolution suggestions for a conflict
   */
  private static async generateResolutionSuggestions(
    conflict: AssignmentConflict,
    memberQualifications: MemberQualification[],
    memberAvailability: Map<string, AvailabilityPattern[]>,
    existingAssignments: ConflictAssignment[]
  ): Promise<ResolutionSuggestion[]> {
    const suggestions: ResolutionSuggestion[] = [];

    switch (conflict.type) {
      case 'scheduling':
        // Suggest reassigning one of the conflicting parts
        suggestions.push({
          type: 'reassign',
          description: 'Reasignar una de las partes a otro miembro calificado',
          confidence: 85,
          alternativeMembers: await this.findAlternativeMembers(
            conflict.affectedAssignments[0],
            memberQualifications,
            memberAvailability,
            existingAssignments
          ),
        });
        break;

      case 'qualification':
        // Suggest reassigning to qualified member
        suggestions.push({
          type: 'reassign',
          description: 'Reasignar a un miembro calificado',
          confidence: 90,
          alternativeMembers: await this.findAlternativeMembers(
            conflict.affectedAssignments[0],
            memberQualifications,
            memberAvailability,
            existingAssignments
          ),
        });
        break;

      case 'availability':
        // Suggest reassigning or rescheduling
        suggestions.push({
          type: 'reassign',
          description: 'Reasignar a un miembro disponible',
          confidence: 80,
          alternativeMembers: await this.findAlternativeMembers(
            conflict.affectedAssignments[0],
            memberQualifications,
            memberAvailability,
            existingAssignments
          ),
        });
        break;

      case 'frequency':
        // Suggest redistributing assignments
        suggestions.push({
          type: 'reassign',
          description: 'Redistribuir asignaciones para balancear la carga',
          confidence: 70,
        });
        break;

      case 'workload':
        // Suggest redistributing workload
        suggestions.push({
          type: 'reassign',
          description: 'Redistribuir la carga de trabajo',
          confidence: 60,
        });
        break;
    }

    return suggestions;
  }

  /**
   * Find alternative members for an assignment
   */
  private static async findAlternativeMembers(
    assignment: ConflictAssignment,
    memberQualifications: MemberQualification[],
    memberAvailability: Map<string, AvailabilityPattern[]>,
    existingAssignments: ConflictAssignment[]
  ): Promise<AlternativeMember[]> {
    const alternatives: AlternativeMember[] = [];
    const partRequirements = this.getPartRequirements(assignment.partTitle);

    for (const memberQual of memberQualifications) {
      if (memberQual.memberId === assignment.memberId) continue;

      // Check qualifications
      const qualificationMatch = this.calculateQualificationMatch(memberQual, partRequirements);
      if (qualificationMatch < 50) continue; // Minimum threshold

      // Check availability
      const availability = memberAvailability.get(memberQual.memberId);
      const availabilityScore = this.calculateAvailabilityScore(assignment.meetingDate, availability);

      // Check current workload
      const currentAssignments = existingAssignments.filter(a => a.memberId === memberQual.memberId);
      const workloadScore = this.calculateWorkloadScore(currentAssignments.length);

      const overallScore = (qualificationMatch + availabilityScore + workloadScore) / 3;

      alternatives.push({
        memberId: memberQual.memberId,
        memberName: `Member ${memberQual.memberId}`, // Would be loaded from database
        qualificationMatch,
        availabilityScore,
        workloadScore,
        overallScore,
      });
    }

    return alternatives.sort((a, b) => b.overallScore - a.overallScore).slice(0, 5);
  }

  /**
   * Get part requirements based on part title/type
   */
  private static getPartRequirements(partTitle: string): string[] {
    // This would be configurable based on congregation needs
    const requirements: { [key: string]: string[] } = {
      'Tesoros de la Palabra de Dios': ['elder', 'ministerial_servant'],
      'Lectura de la Biblia': ['male'],
      'Primera conversación': ['any'],
      'Revisita': ['any'],
      'Estudio bíblico': ['any'],
      'Vida cristiana': ['elder', 'ministerial_servant'],
    };

    for (const [pattern, reqs] of Object.entries(requirements)) {
      if (partTitle.toLowerCase().includes(pattern.toLowerCase())) {
        return reqs;
      }
    }

    return ['any'];
  }

  /**
   * Calculate qualification match percentage
   */
  private static calculateQualificationMatch(
    memberQual: MemberQualification,
    requirements: string[]
  ): number {
    if (requirements.includes('any')) return 100;

    const matches = requirements.filter(req => memberQual.qualifications.includes(req));
    return (matches.length / requirements.length) * 100;
  }

  /**
   * Calculate availability score for a specific date
   */
  private static calculateAvailabilityScore(
    date: Date,
    availability?: AvailabilityPattern[]
  ): number {
    if (!availability) return 50; // Default score

    const dayOfWeek = date.getDay();
    const pattern = availability.find(p => p.dayOfWeek === dayOfWeek);
    
    if (!pattern) return 50;
    if (!pattern.available) return 0;
    
    // Check date range
    if (pattern.startDate && date < pattern.startDate) return 0;
    if (pattern.endDate && date > pattern.endDate) return 0;
    
    return 100;
  }

  /**
   * Calculate workload score (lower assignments = higher score)
   */
  private static calculateWorkloadScore(assignmentCount: number): number {
    // Inverse relationship: fewer assignments = higher score
    const maxAssignments = 10; // Configurable
    return Math.max(0, (maxAssignments - assignmentCount) / maxAssignments * 100);
  }

  /**
   * Load existing assignments for conflict checking
   */
  private static async loadExistingAssignments(
    congregationId: string,
    timeWindowDays: number
  ): Promise<ConflictAssignment[]> {
    const startDate = new Date();
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + timeWindowDays);

    // This would load from the database
    // For now, return empty array
    return [];
  }

  /**
   * Load member qualifications
   */
  private static async loadMemberQualifications(congregationId: string): Promise<MemberQualification[]> {
    // This would load from the database
    // For now, return empty array
    return [];
  }

  /**
   * Load member availability patterns
   */
  private static async loadMemberAvailability(congregationId: string): Promise<Map<string, AvailabilityPattern[]>> {
    // This would load from the database
    // For now, return empty map
    return new Map();
  }

  /**
   * Get default conflict detection options
   */
  private static getDefaultOptions(): ConflictDetectionOptions {
    return {
      checkScheduling: true,
      checkQualifications: true,
      checkAvailability: true,
      checkFrequency: true,
      checkWorkload: true,
      timeWindow: 90, // 3 months
      maxAssignmentsPerMonth: 4,
    };
  }
}
