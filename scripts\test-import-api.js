/**
 * Test script for the midweek meeting import API
 *
 * This script tests the new import endpoint to ensure it works correctly
 * with the existing wol-scraper.js functionality.
 */

// Use built-in fetch (Node.js 18+) or fallback
const fetch = globalThis.fetch || require('node-fetch');

const API_BASE_URL = 'http://localhost:3001';

// Test data
const testData = {
  startDate: '2025-01-27',
  endDate: '2025-02-03',
  language: 'es'
};

// Mock authentication token (you'll need to replace this with a real token)
const AUTH_TOKEN = 'your-auth-token-here';

async function testImportAPI() {
  console.log('🧪 Testing Midweek Meeting Import API...\n');

  try {
    console.log('📤 Making request to import endpoint...');
    console.log('Request data:', JSON.stringify(testData, null, 2));

    const response = await fetch(`${API_BASE_URL}/api/meetings/midweek/import`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${AUTH_TOKEN}`,
      },
      body: JSON.stringify(testData),
    });

    console.log(`📥 Response status: ${response.status}`);

    const responseData = await response.json();
    console.log('📄 Response data:', JSON.stringify(responseData, null, 2));

    if (response.ok) {
      console.log('\n✅ Import API test completed successfully!');
      console.log(`📊 Results: ${responseData.importedCount} imported, ${responseData.skippedCount} skipped, ${responseData.errorCount} errors`);
    } else {
      console.log('\n❌ Import API test failed!');
      console.log('Error:', responseData.error);
    }

  } catch (error) {
    console.error('\n💥 Test failed with error:', error.message);
  }
}

async function testWithoutAuth() {
  console.log('\n🔒 Testing without authentication (should fail)...');

  try {
    const response = await fetch(`${API_BASE_URL}/api/meetings/midweek/import`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData),
    });

    const responseData = await response.json();
    console.log(`📥 Response status: ${response.status}`);
    console.log('📄 Response data:', JSON.stringify(responseData, null, 2));

    if (response.status === 401) {
      console.log('✅ Authentication test passed - correctly rejected unauthorized request');
    } else {
      console.log('❌ Authentication test failed - should have returned 401');
    }

  } catch (error) {
    console.error('💥 Authentication test failed with error:', error.message);
  }
}

async function testInvalidData() {
  console.log('\n📝 Testing with invalid data (should fail)...');

  const invalidData = {
    startDate: 'invalid-date',
    endDate: '2025-02-03',
    language: 'es'
  };

  try {
    const response = await fetch(`${API_BASE_URL}/api/meetings/midweek/import`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${AUTH_TOKEN}`,
      },
      body: JSON.stringify(invalidData),
    });

    const responseData = await response.json();
    console.log(`📥 Response status: ${response.status}`);
    console.log('📄 Response data:', JSON.stringify(responseData, null, 2));

    if (response.status === 400) {
      console.log('✅ Validation test passed - correctly rejected invalid data');
    } else {
      console.log('❌ Validation test failed - should have returned 400');
    }

  } catch (error) {
    console.error('💥 Validation test failed with error:', error.message);
  }
}

// Run tests
async function runAllTests() {
  console.log('🚀 Starting API tests...\n');

  await testWithoutAuth();
  await testInvalidData();

  console.log('\n📝 Note: To test the full import functionality, you need to:');
  console.log('1. Get a valid authentication token from the login endpoint');
  console.log('2. Replace AUTH_TOKEN in this script with the real token');
  console.log('3. Run: node scripts/test-import-api.js');

  console.log('\n🏁 All tests completed!');
}

if (require.main === module) {
  runAllTests();
}

module.exports = {
  testImportAPI,
  testWithoutAuth,
  testInvalidData,
};
