'use client';

/**
 * Midweek Meeting Import Modal
 * 
 * Modal component for importing midweek meeting data from JW.org
 * with date range selection, language options, and progress tracking.
 */

import React, { useState } from 'react';
import Modal from '@/components/ui/Modal';
import { CalendarIcon, GlobeAltIcon, ArrowDownTrayIcon } from '@heroicons/react/24/outline';

interface ImportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onImportComplete: () => void;
}

interface ImportProgress {
  isImporting: boolean;
  currentStep: string;
  progress: number;
  importedCount: number;
  skippedCount: number;
  errorCount: number;
  errors: string[];
}

interface ImportResponse {
  success: boolean;
  message: string;
  importedCount: number;
  skippedCount: number;
  errorCount: number;
  errors: string[];
  dateRange: {
    startDate: string;
    endDate: string;
  };
}

export default function MidweekImportModal({ isOpen, onClose, onImportComplete }: ImportModalProps) {
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [language, setLanguage] = useState('es');
  const [progress, setProgress] = useState<ImportProgress>({
    isImporting: false,
    currentStep: '',
    progress: 0,
    importedCount: 0,
    skippedCount: 0,
    errorCount: 0,
    errors: [],
  });
  const [importResult, setImportResult] = useState<ImportResponse | null>(null);

  // Get today's date for min date validation
  const today = new Date().toISOString().split('T')[0];

  // Get max date (6 months from today)
  const maxDate = new Date();
  maxDate.setMonth(maxDate.getMonth() + 6);
  const maxDateString = maxDate.toISOString().split('T')[0];

  const handleImport = async () => {
    if (!startDate || !endDate) {
      alert('Por favor selecciona las fechas de inicio y fin');
      return;
    }

    if (new Date(startDate) > new Date(endDate)) {
      alert('La fecha de inicio debe ser anterior a la fecha de fin');
      return;
    }

    setProgress({
      isImporting: true,
      currentStep: 'Iniciando importación...',
      progress: 0,
      importedCount: 0,
      skippedCount: 0,
      errorCount: 0,
      errors: [],
    });
    setImportResult(null);

    try {
      const response = await fetch('/api/meetings/midweek/import', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          startDate,
          endDate,
          language,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Error en la importación');
      }

      const result: ImportResponse = await response.json();
      setImportResult(result);

      setProgress(prev => ({
        ...prev,
        isImporting: false,
        currentStep: 'Importación completada',
        progress: 100,
        importedCount: result.importedCount,
        skippedCount: result.skippedCount,
        errorCount: result.errorCount,
        errors: result.errors,
      }));

      // Call the completion callback to refresh the calendar
      onImportComplete();

    } catch (error) {
      console.error('Import error:', error);
      setProgress(prev => ({
        ...prev,
        isImporting: false,
        currentStep: 'Error en la importación',
        progress: 0,
        errorCount: 1,
        errors: [error instanceof Error ? error.message : 'Error desconocido'],
      }));
    }
  };

  const handleClose = () => {
    if (!progress.isImporting) {
      setProgress({
        isImporting: false,
        currentStep: '',
        progress: 0,
        importedCount: 0,
        skippedCount: 0,
        errorCount: 0,
        errors: [],
      });
      setImportResult(null);
      setStartDate('');
      setEndDate('');
      onClose();
    }
  };

  const isFormValid = startDate && endDate && !progress.isImporting;

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Importar Reuniones desde JW.org"
      size="lg"
    >
      <div className="space-y-6">
        {/* Date Range Selection */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <CalendarIcon className="h-4 w-4 inline mr-1" />
              Fecha de Inicio
            </label>
            <input
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              min={today}
              max={maxDateString}
              disabled={progress.isImporting}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent disabled:bg-gray-100"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <CalendarIcon className="h-4 w-4 inline mr-1" />
              Fecha de Fin
            </label>
            <input
              type="date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              min={startDate || today}
              max={maxDateString}
              disabled={progress.isImporting}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent disabled:bg-gray-100"
            />
          </div>
        </div>

        {/* Language Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <GlobeAltIcon className="h-4 w-4 inline mr-1" />
            Idioma
          </label>
          <select
            value={language}
            onChange={(e) => setLanguage(e.target.value)}
            disabled={progress.isImporting}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent disabled:bg-gray-100"
          >
            <option value="es">Español</option>
            <option value="en">English</option>
          </select>
        </div>

        {/* Progress Section */}
        {(progress.isImporting || importResult) && (
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-3">Estado de la Importación</h4>
            
            {progress.isImporting && (
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-500"></div>
                  <span className="text-sm text-gray-600">{progress.currentStep}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${progress.progress}%` }}
                  ></div>
                </div>
              </div>
            )}

            {importResult && (
              <div className="space-y-2">
                <div className={`text-sm font-medium ${importResult.success ? 'text-green-600' : 'text-red-600'}`}>
                  {importResult.message}
                </div>
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div className="text-center">
                    <div className="font-medium text-green-600">{progress.importedCount}</div>
                    <div className="text-gray-500">Importadas</div>
                  </div>
                  <div className="text-center">
                    <div className="font-medium text-yellow-600">{progress.skippedCount}</div>
                    <div className="text-gray-500">Omitidas</div>
                  </div>
                  <div className="text-center">
                    <div className="font-medium text-red-600">{progress.errorCount}</div>
                    <div className="text-gray-500">Errores</div>
                  </div>
                </div>
                
                {progress.errors.length > 0 && (
                  <div className="mt-3">
                    <details className="text-sm">
                      <summary className="cursor-pointer text-red-600 font-medium">
                        Ver errores ({progress.errors.length})
                      </summary>
                      <div className="mt-2 space-y-1">
                        {progress.errors.map((error, index) => (
                          <div key={index} className="text-red-600 text-xs bg-red-50 p-2 rounded">
                            {error}
                          </div>
                        ))}
                      </div>
                    </details>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button
            onClick={handleClose}
            disabled={progress.isImporting}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50"
          >
            {importResult ? 'Cerrar' : 'Cancelar'}
          </button>
          {!importResult && (
            <button
              onClick={handleImport}
              disabled={!isFormValid}
              className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              <ArrowDownTrayIcon className="h-4 w-4" />
              <span>{progress.isImporting ? 'Importando...' : 'Importar'}</span>
            </button>
          )}
        </div>
      </div>
    </Modal>
  );
}
