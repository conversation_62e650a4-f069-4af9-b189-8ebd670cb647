'use client';

/**
 * Midweek Meeting Import Modal
 *
 * Modal component for importing midweek meeting data from JW.org
 * with week selection matching the original SalonDelReino implementation.
 * Uses the wol-scraper fetchWeeks function to display available weeks.
 */

import React, { useState, useEffect } from 'react';
import Modal from '@/components/ui/Modal';
import { CalendarIcon, GlobeAltIcon, ArrowDownTrayIcon, XMarkIcon } from '@heroicons/react/24/outline';

interface ImportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onImportComplete: () => void;
}

interface Week {
  week_id: string;
  workbook_id: string;
  title: string;
  url: string;
  start_date: string;
  end_date: string;
  week_number: number;
}

interface ImportProgress {
  isImporting: boolean;
  currentStep: string;
  progress: number;
  importedCount: number;
  skippedCount: number;
  errorCount: number;
  errors: string[];
}

interface ImportResponse {
  success: boolean;
  message: string;
  importedCount: number;
  skippedCount: number;
  errorCount: number;
  errors: string[];
  dateRange: {
    startDate: string;
    endDate: string;
  };
}

export default function MidweekImportModal({ isOpen, onClose, onImportComplete }: ImportModalProps) {
  const [availableWeeks, setAvailableWeeks] = useState<Week[]>([]);
  const [selectedWeeks, setSelectedWeeks] = useState<Set<string>>(new Set());
  const [language, setLanguage] = useState('es');
  const [loading, setLoading] = useState(false);
  const [progress, setProgress] = useState<ImportProgress>({
    isImporting: false,
    currentStep: '',
    progress: 0,
    importedCount: 0,
    skippedCount: 0,
    errorCount: 0,
    errors: [],
  });
  const [importResult, setImportResult] = useState<ImportResponse | null>(null);

  // Fetch available weeks when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchAvailableWeeks();
    }
  }, [isOpen]);

  const fetchAvailableWeeks = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/meetings/midweek/weeks', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const weeks = await response.json();
        setAvailableWeeks(weeks);
      } else {
        console.error('Failed to fetch weeks');
        // Fallback to current year weeks
        const currentYear = new Date().getFullYear();
        const fallbackWeeks = generateFallbackWeeks(currentYear);
        setAvailableWeeks(fallbackWeeks);
      }
    } catch (error) {
      console.error('Error fetching weeks:', error);
      // Fallback to current year weeks
      const currentYear = new Date().getFullYear();
      const fallbackWeeks = generateFallbackWeeks(currentYear);
      setAvailableWeeks(fallbackWeeks);
    } finally {
      setLoading(false);
    }
  };

  const generateFallbackWeeks = (year: number): Week[] => {
    const weeks: Week[] = [];
    const monthNames = [
      'enero', 'febrero', 'marzo', 'abril', 'mayo', 'junio',
      'julio', 'agosto', 'septiembre', 'octubre', 'noviembre', 'diciembre'
    ];

    // Generate a few weeks around current date as fallback
    const currentDate = new Date();
    const currentWeek = Math.ceil((currentDate.getTime() - new Date(year, 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000));

    for (let i = Math.max(1, currentWeek - 2); i <= Math.min(52, currentWeek + 10); i++) {
      const startDate = new Date(year, 0, 1 + (i - 1) * 7);
      const endDate = new Date(startDate);
      endDate.setDate(startDate.getDate() + 6);

      const title = `Semana del ${startDate.getDate()} de ${monthNames[startDate.getMonth()]} al ${endDate.getDate()} de ${monthNames[endDate.getMonth()]} de ${year}`;

      weeks.push({
        week_id: `${year}/${i}`,
        workbook_id: `${year}`,
        title,
        url: `https://wol.jw.org/es/wol/meetings/r4/lp-s/${year}/${i}`,
        start_date: startDate.toISOString().split('T')[0],
        end_date: endDate.toISOString().split('T')[0],
        week_number: i
      });
    }

    return weeks;
  };

  const handleWeekToggle = (weekId: string) => {
    const newSelected = new Set(selectedWeeks);
    if (newSelected.has(weekId)) {
      newSelected.delete(weekId);
    } else {
      newSelected.add(weekId);
    }
    setSelectedWeeks(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedWeeks.size === availableWeeks.length) {
      setSelectedWeeks(new Set());
    } else {
      setSelectedWeeks(new Set(availableWeeks.map(week => week.week_id)));
    }
  };

  const handleImport = async () => {
    if (selectedWeeks.size === 0) {
      alert('Por favor selecciona al menos una semana para importar');
      return;
    }

    setProgress({
      isImporting: true,
      currentStep: 'Iniciando importación...',
      progress: 0,
      importedCount: 0,
      skippedCount: 0,
      errorCount: 0,
      errors: [],
    });
    setImportResult(null);

    try {
      // Convert selected weeks to the format expected by the API
      const selectedWeeksList = Array.from(selectedWeeks).map(weekId => {
        const week = availableWeeks.find(w => w.week_id === weekId);
        return {
          year: parseInt(weekId.split('/')[0]),
          weekNumber: parseInt(weekId.split('/')[1]),
          startDate: week?.start_date,
          endDate: week?.end_date
        };
      });

      const response = await fetch('/api/meetings/midweek/import', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          weeks: selectedWeeksList,
          language,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Error en la importación');
      }

      const result: ImportResponse = await response.json();
      setImportResult(result);

      setProgress(prev => ({
        ...prev,
        isImporting: false,
        currentStep: 'Importación completada',
        progress: 100,
        importedCount: result.importedCount,
        skippedCount: result.skippedCount,
        errorCount: result.errorCount,
        errors: result.errors,
      }));

      // Call the completion callback to refresh the calendar
      onImportComplete();

    } catch (error) {
      console.error('Import error:', error);
      setProgress(prev => ({
        ...prev,
        isImporting: false,
        currentStep: 'Error en la importación',
        progress: 0,
        errorCount: 1,
        errors: [error instanceof Error ? error.message : 'Error desconocido'],
      }));
    }
  };

  const handleClose = () => {
    if (!progress.isImporting) {
      setProgress({
        isImporting: false,
        currentStep: '',
        progress: 0,
        importedCount: 0,
        skippedCount: 0,
        errorCount: 0,
        errors: [],
      });
      setImportResult(null);
      setSelectedWeeks(new Set());
      setAvailableWeeks([]);
      onClose();
    }
  };

  const isFormValid = selectedWeeks.size > 0 && !progress.isImporting;

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Seleccione una semana"
      size="lg"
    >
      <div className="space-y-4">
        {/* Language Selection */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <GlobeAltIcon className="h-4 w-4 text-gray-500" />
            <select
              value={language}
              onChange={(e) => setLanguage(e.target.value)}
              disabled={progress.isImporting || loading}
              className="px-3 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-purple-500 focus:border-transparent disabled:bg-gray-100"
            >
              <option value="es">Español</option>
              <option value="en">English</option>
            </select>
          </div>

          {availableWeeks.length > 0 && (
            <button
              onClick={handleSelectAll}
              disabled={progress.isImporting || loading}
              className="text-sm text-purple-600 hover:text-purple-800 disabled:text-gray-400"
            >
              {selectedWeeks.size === availableWeeks.length ? 'Deseleccionar todo' : 'Seleccionar todo'}
            </button>
          )}
        </div>

        {/* Week Selection List */}
        <div className="border border-gray-300 rounded-lg max-h-80 overflow-y-auto">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-500"></div>
              <span className="ml-2 text-gray-600">Cargando semanas...</span>
            </div>
          ) : availableWeeks.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No hay semanas disponibles
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {availableWeeks.map((week) => (
                <label
                  key={week.week_id}
                  className="flex items-center px-4 py-3 hover:bg-gray-50 cursor-pointer"
                >
                  <input
                    type="checkbox"
                    checked={selectedWeeks.has(week.week_id)}
                    onChange={() => handleWeekToggle(week.week_id)}
                    disabled={progress.isImporting}
                    className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded disabled:opacity-50"
                  />
                  <span className="ml-3 text-sm text-gray-900 flex-1">
                    {week.title}
                  </span>
                </label>
              ))}
            </div>
          )}
        </div>

        {selectedWeeks.size > 0 && (
          <div className="text-sm text-gray-600 bg-purple-50 px-3 py-2 rounded">
            {selectedWeeks.size} semana{selectedWeeks.size !== 1 ? 's' : ''} seleccionada{selectedWeeks.size !== 1 ? 's' : ''}
          </div>
        )}

        {/* Progress Section */}
        {(progress.isImporting || importResult) && (
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-3">Estado de la Importación</h4>

            {progress.isImporting && (
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-500"></div>
                  <span className="text-sm text-gray-600">{progress.currentStep}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${progress.progress}%` }}
                  ></div>
                </div>
              </div>
            )}

            {importResult && (
              <div className="space-y-2">
                <div className={`text-sm font-medium ${importResult.success ? 'text-green-600' : 'text-red-600'}`}>
                  {importResult.message}
                </div>
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div className="text-center">
                    <div className="font-medium text-green-600">{progress.importedCount}</div>
                    <div className="text-gray-500">Importadas</div>
                  </div>
                  <div className="text-center">
                    <div className="font-medium text-yellow-600">{progress.skippedCount}</div>
                    <div className="text-gray-500">Omitidas</div>
                  </div>
                  <div className="text-center">
                    <div className="font-medium text-red-600">{progress.errorCount}</div>
                    <div className="text-gray-500">Errores</div>
                  </div>
                </div>

                {progress.errors.length > 0 && (
                  <div className="mt-3">
                    <details className="text-sm">
                      <summary className="cursor-pointer text-red-600 font-medium">
                        Ver errores ({progress.errors.length})
                      </summary>
                      <div className="mt-2 space-y-1">
                        {progress.errors.map((error, index) => (
                          <div key={index} className="text-red-600 text-xs bg-red-50 p-2 rounded">
                            {error}
                          </div>
                        ))}
                      </div>
                    </details>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button
            onClick={handleClose}
            disabled={progress.isImporting}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50"
          >
            {importResult ? 'Cerrar' : 'Cancelar'}
          </button>
          {!importResult && (
            <button
              onClick={handleImport}
              disabled={!isFormValid}
              className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              <ArrowDownTrayIcon className="h-4 w-4" />
              <span>{progress.isImporting ? 'Importando...' : 'Importar Seleccionadas'}</span>
            </button>
          )}
        </div>
      </div>
    </Modal>
  );
}
