# Story 14.3: Live Meeting Data Integration and Editing

## Status
InProgress

## Story
**As a** congregation administrator,
**I want** to view and edit real meeting data from JW.org in the admin interface,
**so that** I can manage actual meeting schedules, assign members to parts, and make necessary adjustments to imported meeting content.

## Acceptance Criteria
1. Calendar view displays real meeting data from database instead of static placeholder data
2. Meeting details modal shows actual JW.org content (theme, scripture, parts) when viewing meetings
3. Enable editing of meeting assignments (chairman, prayers, part assignments) for imported meetings
4. Meeting parts display with correct titles, time allocations, and assignment capabilities
5. Save edited meeting data back to database with proper validation
6. Maintain existing UI layout and purple theme while displaying dynamic content
7. Handle missing or incomplete meeting data gracefully with appropriate fallbacks
8. Assignment dropdowns populate with eligible members based on part requirements
9. Changes to meeting assignments are immediately reflected in the interface
10. Existing member configuration functionality continues to work unchanged

## Tasks / Subtasks
- [x] Connect calendar view to live data (AC: 1, 6)
  - [x] Replace static meeting data with database queries
  - [x] Update calendar component to fetch from /api/meetings/midweek
  - [x] Implement loading states for calendar data
  - [x] Handle empty states when no meetings are imported

- [x] Enhance meeting details modal (AC: 2, 4)
  - [x] Display real meeting themes and scriptures from JW.org
  - [x] Show actual meeting parts with correct titles and time allocations
  - [x] Implement dynamic part rendering based on database content
  - [x] Add fallback content for missing or incomplete data

- [ ] Implement meeting assignment editing (AC: 3, 8)
  - [ ] Add edit mode to meeting details modal
  - [ ] Create assignment dropdowns for chairman, prayers, and parts
  - [ ] Populate dropdowns with eligible members based on qualifications
  - [ ] Implement assignment validation and conflict checking

- [ ] Add save functionality (AC: 5, 9)
  - [ ] Create PUT endpoint for updating meeting assignments
  - [ ] Implement optimistic UI updates for better user experience
  - [ ] Add proper error handling for save operations
  - [ ] Refresh meeting data after successful saves

- [ ] Data handling and fallbacks (AC: 7, 10)
  - [ ] Implement graceful handling of missing meeting data
  - [ ] Add fallback content for incomplete JW.org imports
  - [ ] Ensure existing member configuration remains functional
  - [ ] Test integration with existing meeting management features

## Dev Notes

**Relevant Source Tree Information:**
- `/src/app/admin/meetings/midweek/page.tsx` - Main admin interface to enhance
- `/src/app/api/meetings/midweek/route.ts` - GET endpoint for fetching meetings
- `/prisma/schema.prisma` - MidweekMeeting and MidweekMeetingPart models
- `/src/components/meetings/` - Meeting-related UI components
- `/src/hooks/useMeetings.ts` - Custom hook for meeting data management

**Key Implementation Context:**
- Calendar currently shows static sample data that needs to be replaced
- Meeting parts have specific qualification requirements (elders, ministerial servants, publishers)
- Database stores meeting data with congregationId isolation
- Assignment logic should respect member qualifications and availability
- UI should maintain existing purple theme and responsive design

**Data Flow Integration:**
- Calendar fetches meetings using existing API with date range filtering
- Meeting details modal receives meeting ID and fetches full meeting data
- Assignment editing requires member data with qualification information
- Save operations update both MidweekMeeting and MidweekMeetingPart records
- Real-time updates ensure UI reflects current database state

**Member Assignment Logic:**
- Chairman: Elders and Ministerial Servants
- Prayers: Elders, Ministerial Servants, and qualified Publishers
- Treasures/Gems/Christian Life: Elders and Ministerial Servants
- Bible Reading/Ministry Parts: Ministerial Servants and Publishers
- Use existing member configuration data for qualification checking

**Critical Integration Requirements:**
- Preserve existing member configuration functionality
- Maintain congregation isolation for multi-tenant support
- Handle partial meeting data gracefully (some parts may be missing)
- Implement proper loading and error states throughout the interface
- Ensure mobile responsiveness is maintained with dynamic content

### Testing

**Test File Location:** `/tests/components/admin/MidweekMeetingManagement.test.tsx`

**Testing Standards:**
- Test dynamic data loading and display
- Mock API responses for different meeting data scenarios
- Test assignment editing and validation logic
- Test error handling for incomplete or missing data

**Specific Testing Requirements:**
- Test calendar view with real meeting data vs empty state
- Test meeting details modal with complete and incomplete data
- Test assignment dropdown population and filtering
- Test save functionality with success and error scenarios
- Test member qualification validation for assignments
- Test responsive design with dynamic content
- Mock member data and meeting data for consistent testing

**Testing Frameworks:**
- React Testing Library for component interactions
- Jest for business logic testing
- MSW for API endpoint mocking
- Test database scenarios with Prisma test client
- Follow existing patterns from meeting management tests

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-01-28 | 1.0 | Initial story creation for live data integration and editing | Product Manager |

## Dev Agent Record
_(This section is owned by dev-agent and can only be modified by dev-agent)_

### Agent Model Used
Claude Sonnet 4 by Anthropic

### Debug Log References
_(To be populated by dev-agent during implementation)_

### Completion Notes List
- ✅ **CRITICAL FIX**: Replaced date range picker with week selection UI matching original SalonDelReino implementation
- ✅ Updated MidweekImportModal to use wol-scraper fetchWeeks function for authentic week titles
- ✅ Created /api/meetings/midweek/weeks endpoint to fetch available weeks using wol-scraper
- ✅ Implemented week selection with checkboxes matching the screenshot pattern
- ✅ Added "Seleccionar todo" / "Deseleccionar todo" functionality
- ✅ Updated import API to handle week-based selection instead of date ranges
- ✅ Added fallback week generation when wol-scraper is unavailable
- ✅ Maintained Spanish week titles format: "Semana del X de mes al Y de mes de año"
- ✅ Fixed import logic to process selected weeks individually
- ✅ Added proper validation for week numbers and year ranges
- ✅ **LIVE DATA INTEGRATION**: Connected calendar view to fetch real meeting data from database
- ✅ Implemented proper loading states for both current and past meetings
- ✅ Added empty state handling with helpful import prompts
- ✅ Separated current and past meetings based on date comparison
- ✅ Added automatic data refresh after successful import
- ✅ Replaced static sample data with dynamic database queries
- ✅ Maintained existing UI layout while adding live data functionality

### File List
- `src/components/meetings/MidweekImportModal.tsx` - Completely rewritten to match original UI pattern
- `src/app/api/meetings/midweek/weeks/route.ts` - New endpoint for fetching available weeks
- `src/app/api/meetings/midweek/import/route.ts` - Updated to handle week-based import
- `wol-scraper.js` - Verified fetchWeeks function integration
- `src/app/admin/meetings/midweek/page.tsx` - Updated to fetch live data and handle loading states

## QA Results
_(This section is owned by qa-agent and can only be modified by qa-agent)_

_(To be populated by qa-agent after story completion)_
