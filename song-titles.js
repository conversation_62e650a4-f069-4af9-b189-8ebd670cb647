/**
 * Song Titles Database
 * 
 * Provides fallback song titles for the wol-scraper when dynamic fetching fails.
 * This integrates with the existing songs database in the Hermanos project.
 */

const { PrismaClient } = require('@prisma/client');

let prisma;

// Initialize Prisma client
function initializePrisma() {
  if (!prisma) {
    prisma = new PrismaClient();
  }
  return prisma;
}

/**
 * Get song title from database
 * @param {number} songNumber - The song number
 * @param {string} language - Language code ('es' for Spanish, 'en' for English)
 * @returns {string|null} Song title or null if not found
 */
function getSongTitle(songNumber, language = 'es') {
  try {
    // For now, return a generic title since we need to handle async database calls differently
    // In the actual implementation, this would need to be refactored to be async
    // or use a pre-loaded cache
    
    if (language === 'es') {
      return `Canción ${songNumber}`;
    } else {
      return `Song ${songNumber}`;
    }
  } catch (error) {
    console.error(`Error getting song title for ${songNumber}:`, error);
    return null;
  }
}

/**
 * Get song title from database (async version)
 * @param {number} songNumber - The song number
 * @param {string} language - Language code ('es' for Spanish, 'en' for English)
 * @returns {Promise<string|null>} Song title or null if not found
 */
async function getSongTitleAsync(songNumber, language = 'es') {
  try {
    const client = initializePrisma();
    
    const song = await client.song.findUnique({
      where: { songNumber },
      select: {
        titleEs: true,
        titleEn: true,
      },
    });

    if (song) {
      if (language === 'es' && song.titleEs) {
        return song.titleEs;
      } else if (language === 'en' && song.titleEn) {
        return song.titleEn;
      }
    }

    // Fallback to generic title
    return language === 'es' ? `Canción ${songNumber}` : `Song ${songNumber}`;
  } catch (error) {
    console.error(`Error getting song title for ${songNumber}:`, error);
    // Fallback to generic title
    return language === 'es' ? `Canción ${songNumber}` : `Song ${songNumber}`;
  }
}

/**
 * Preload song titles into memory cache
 * @param {string} language - Language to preload
 * @returns {Promise<Object>} Cache object with song titles
 */
async function preloadSongTitles(language = 'es') {
  try {
    const client = initializePrisma();
    
    const songs = await client.song.findMany({
      where: { isActive: true },
      select: {
        songNumber: true,
        titleEs: true,
        titleEn: true,
      },
      orderBy: { songNumber: 'asc' },
    });

    const cache = {};
    songs.forEach(song => {
      if (language === 'es' && song.titleEs) {
        cache[song.songNumber] = song.titleEs;
      } else if (language === 'en' && song.titleEn) {
        cache[song.songNumber] = song.titleEn;
      } else {
        // Fallback
        cache[song.songNumber] = language === 'es' ? `Canción ${song.songNumber}` : `Song ${song.songNumber}`;
      }
    });

    return cache;
  } catch (error) {
    console.error('Error preloading song titles:', error);
    return {};
  }
}

module.exports = {
  getSongTitle,
  getSongTitleAsync,
  preloadSongTitles,
};
